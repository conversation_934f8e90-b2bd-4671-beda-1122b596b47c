pub mod builder;
pub mod watcher;
pub mod change_detector;
pub mod consistency;
pub mod versioning;
pub mod progress;

pub use builder::{IndexBuilder, IndexOperation, IndexTransaction};
pub use watcher::{FileWatcher, WatchEvent, WatcherConfig};
pub use change_detector::{ChangeDete<PERSON>, FileChange, ChangeType};
pub use consistency::{Consist<PERSON><PERSON><PERSON><PERSON>, IndexHealth, RepairAction};
pub use versioning::{IndexVersion, VersionManager, MigrationPlan};
pub use progress::{ProgressReporter, IndexingProgress, ProgressEvent};

use crate::error::Result;
use std::path::Path;
use tokio::sync::mpsc;

#[derive(Debug, Clone)]
pub struct IndexingConfig {
    pub batch_size: usize,
    pub debounce_ms: u64,
    pub max_concurrent_operations: usize,
    pub consistency_check_interval_ms: u64,
    pub enable_background_optimization: bool,
    pub transaction_timeout_ms: u64,
    pub max_retry_attempts: u32,
    pub checkpoint_interval_ms: u64,
}

impl Default for IndexingConfig {
    fn default() -> Self {
        Self {
            batch_size: 100,
            debounce_ms: 500,
            max_concurrent_operations: 10,
            consistency_check_interval_ms: 60000, // 1 minute
            enable_background_optimization: true,
            transaction_timeout_ms: 30000, // 30 seconds
            max_retry_attempts: 3,
            checkpoint_interval_ms: 10000, // 10 seconds
        }
    }
}

#[derive(Debug, Clone)]
pub enum IndexingEvent {
    Started { operation_id: String },
    Progress { operation_id: String, progress: f64 },
    Completed { operation_id: String, items_processed: usize },
    Failed { operation_id: String, error: String },
    BatchProcessed { batch_id: String, items: usize },
    ConsistencyCheckStarted,
    ConsistencyCheckCompleted { issues_found: usize },
    BackgroundOptimizationStarted,
    BackgroundOptimizationCompleted,
}

pub type IndexingEventSender = mpsc::UnboundedSender<IndexingEvent>;
pub type IndexingEventReceiver = mpsc::UnboundedReceiver<IndexingEvent>;

pub struct RealTimeIndexer {
    builder: IndexBuilder,
    watcher: FileWatcher,
    change_detector: ChangeDetector,
    consistency_checker: ConsistencyChecker,
    version_manager: VersionManager,
    progress_reporter: ProgressReporter,
    config: IndexingConfig,
    event_sender: IndexingEventSender,
    shutdown_signal: tokio::sync::watch::Receiver<bool>,
}

impl RealTimeIndexer {
    pub async fn new(
        index_path: &Path,
        config: IndexingConfig,
    ) -> Result<(Self, IndexingEventReceiver, tokio::sync::watch::Sender<bool>)> {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        let (shutdown_sender, shutdown_signal) = tokio::sync::watch::channel(false);

        let builder = IndexBuilder::new(index_path, &config).await?;
        let watcher = FileWatcher::new(WatcherConfig::from(&config))?;
        let change_detector = ChangeDetector::new(index_path).await?;
        let consistency_checker = ConsistencyChecker::new(index_path).await?;
        let version_manager = VersionManager::new(index_path).await?;
        let progress_reporter = ProgressReporter::new(event_sender.clone());

        Ok((
            Self {
                builder,
                watcher,
                change_detector,
                consistency_checker,
                version_manager,
                progress_reporter,
                config,
                event_sender,
                shutdown_signal,
            },
            event_receiver,
            shutdown_sender,
        ))
    }

    pub async fn start(&mut self, watch_paths: Vec<&Path>) -> Result<()> {
        // Start file watcher
        self.watcher.start_watching(watch_paths).await?;

        // Start background tasks
        self.start_background_tasks().await?;

        // Main event loop
        self.run_event_loop().await
    }

    async fn start_background_tasks(&self) -> Result<()> {
        // Consistency checker task
        let consistency_checker = self.consistency_checker.clone();
        let config = self.config.clone();
        let event_sender = self.event_sender.clone();
        let mut shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_millis(config.consistency_check_interval_ms)
            );

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        let _ = event_sender.send(IndexingEvent::ConsistencyCheckStarted);
                        
                        match consistency_checker.check_health().await {
                            Ok(health) => {
                                let issues = health.issues.len();
                                let _ = event_sender.send(IndexingEvent::ConsistencyCheckCompleted { issues_found: issues });
                                
                                if issues > 0 {
                                    let _ = consistency_checker.repair(&health).await;
                                }
                            }
                            Err(e) => {
                                tracing::error!("Consistency check failed: {}", e);
                            }
                        }
                    }
                    _ = shutdown_signal.changed() => {
                        if *shutdown_signal.borrow() {
                            break;
                        }
                    }
                }
            }
        });

        Ok(())
    }

    async fn run_event_loop(&mut self) -> Result<()> {
        let mut change_batch = Vec::new();
        let mut debounce_timer = tokio::time::interval(
            std::time::Duration::from_millis(self.config.debounce_ms)
        );

        loop {
            tokio::select! {
                change = self.watcher.next_change() => {
                    if let Some(change) = change {
                        change_batch.push(change);
                        
                        if change_batch.len() >= self.config.batch_size {
                            self.process_change_batch(&mut change_batch).await?;
                        }
                    }
                }
                _ = debounce_timer.tick() => {
                    if !change_batch.is_empty() {
                        self.process_change_batch(&mut change_batch).await?;
                    }
                }
                _ = self.shutdown_signal.changed() => {
                    if *self.shutdown_signal.borrow() {
                        // Process remaining changes before shutdown
                        if !change_batch.is_empty() {
                            self.process_change_batch(&mut change_batch).await?;
                        }
                        break;
                    }
                }
            }
        }

        Ok(())
    }

    async fn process_change_batch(&mut self, changes: &mut Vec<WatchEvent>) -> Result<()> {
        if changes.is_empty() {
            return Ok(());
        }

        let batch_id = uuid::Uuid::new_v4().to_string();
        let batch_size = changes.len();

        // Detect actual changes (filter out duplicates, temporary files, etc.)
        let file_changes = self.change_detector.process_events(changes.drain(..).collect()).await?;

        if file_changes.is_empty() {
            return Ok(());
        }

        // Start transaction
        let transaction = self.builder.begin_transaction().await?;

        // Process changes in parallel batches
        let mut operations = Vec::new();
        for change in file_changes {
            let operation = match change.change_type {
                ChangeType::Created | ChangeType::Modified => {
                    IndexOperation::Update {
                        path: change.path,
                        content: change.content,
                        metadata: change.metadata,
                    }
                }
                ChangeType::Deleted => {
                    IndexOperation::Delete {
                        path: change.path,
                    }
                }
                ChangeType::Moved { from, to } => {
                    IndexOperation::Move { from, to }
                }
            };
            operations.push(operation);
        }

        // Execute operations
        match self.builder.execute_batch(&transaction, operations).await {
            Ok(_) => {
                self.builder.commit_transaction(transaction).await?;
                let _ = self.event_sender.send(IndexingEvent::BatchProcessed {
                    batch_id,
                    items: batch_size,
                });
            }
            Err(e) => {
                self.builder.rollback_transaction(transaction).await?;
                let _ = self.event_sender.send(IndexingEvent::Failed {
                    operation_id: batch_id,
                    error: e.to_string(),
                });
                return Err(e);
            }
        }

        Ok(())
    }

    pub async fn shutdown(&mut self) -> Result<()> {
        // Stop watcher
        self.watcher.stop().await?;

        // Ensure all pending operations complete
        self.builder.flush().await?;

        // Final consistency check
        let health = self.consistency_checker.check_health().await?;
        if !health.issues.is_empty() {
            self.consistency_checker.repair(&health).await?;
        }

        Ok(())
    }
}
