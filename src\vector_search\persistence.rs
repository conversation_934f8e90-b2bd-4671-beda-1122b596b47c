use super::*;
use anyhow::Result;
use memmap2::{Mmap, MmapOptions};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{File, OpenOptions};
use std::io::{<PERSON><PERSON>Read<PERSON>, BufWriter};
use std::path::{Path, PathBuf};


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PersistenceConfig {
    
    pub base_path: PathBuf,
    
    pub enable_compression: bool,
    
    pub backup_count: usize,
}

impl Default for PersistenceConfig {
    fn default() -> Self {
        Self {
            base_path: PathBuf::from("./vector_index"),
            enable_compression: true,
            backup_count: 3,
        }
    }
}


const INDEX_FORMAT_VERSION: u32 = 1;


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct IndexHeader {
    version: u32,
    vector_count: usize,
    dimension: usize,
    distance_metric: DistanceMetric,
    compressed: bool,
    checksum: u64,
}


#[derive(Debug)]
struct MmapIndexFile {
    header_mmap: Mmap,
    vectors_mmap: Mmap,
    metadata_mmap: Mmap,
    graph_mmap: Mmap,
}


pub struct IndexPersistence {
    config: PersistenceConfig,
}

impl IndexPersistence {
    
    pub fn new(config: PersistenceConfig) -> Result<Self> {
        
        std::fs::create_dir_all(&config.base_path)?;
        
        Ok(Self { config })
    }
    
    
    pub async fn save_index(
        &self,
        index: &HnswIndex,
        vectors: &HashMap<VectorId, VectorEntry>,
        path: &PathBuf,
    ) -> Result<()> {
        let start_time = std::time::Instant::now();
        
        
        self.create_backup(path).await?;
        
        
        let header_path = path.with_extension("header");
        let vectors_path = path.with_extension("vectors");
        let metadata_path = path.with_extension("metadata");
        let graph_path = path.with_extension("graph");
        
        
        self.save_header(&header_path, vectors).await?;
        
        
        self.save_vectors_mmap(&vectors_path, vectors).await?;
        
        
        self.save_metadata(&metadata_path, vectors).await?;
        
        
        self.save_graph(&graph_path, index).await?;
        
        let save_time = start_time.elapsed();
        tracing::info!("Index saved in {:.2}s to {:?}", save_time.as_secs_f32(), path);
        
        Ok(())
    }
    
    
    pub async fn load_index(&self, path: &PathBuf) -> Result<(HnswIndex, HashMap<VectorId, VectorEntry>)> {
        let start_time = std::time::Instant::now();
        
        
        let header_path = path.with_extension("header");
        let vectors_path = path.with_extension("vectors");
        let metadata_path = path.with_extension("metadata");
        let graph_path = path.with_extension("graph");
        
        
        for file_path in &[&header_path, &vectors_path, &metadata_path, &graph_path] {
            if !file_path.exists() {
                return Err(anyhow::anyhow!("Index file not found: {:?}", file_path));
            }
        }
        
        
        let header = self.load_header(&header_path).await?;
        
        
        let vectors = self.load_vectors_mmap(&vectors_path, &metadata_path, &header).await?;
        
        
        let index = self.load_graph(&graph_path, &header).await?;
        
        let load_time = start_time.elapsed();
        tracing::info!("Index loaded in {:.2}s from {:?}", load_time.as_secs_f32(), path);
        
        Ok((index, vectors))
    }
    
    
    async fn save_header(&self, path: &PathBuf, vectors: &HashMap<VectorId, VectorEntry>) -> Result<()> {
        let dimension = vectors.values().next()
            .map(|v| v.vector.len())
            .unwrap_or(0);
        
        let header = IndexHeader {
            version: INDEX_FORMAT_VERSION,
            vector_count: vectors.len(),
            dimension,
            distance_metric: DistanceMetric::Cosine, 
            compressed: self.config.enable_compression,
            checksum: self.calculate_checksum(vectors),
        };
        
        let file = File::create(path)?;
        let mut writer = BufWriter::new(file);
        
        if self.config.enable_compression {
            use flate2::{Compression, write::GzEncoder};
            let mut encoder = GzEncoder::new(writer, Compression::default());
            rmp_serde::encode::write(&mut encoder, &header)?;
            encoder.finish()?;
        } else {
            rmp_serde::encode::write(&mut writer, &header)?;
        }
        
        Ok(())
    }
    
    
    async fn load_header(&self, path: &PathBuf) -> Result<IndexHeader> {
        let file = File::open(path)?;
        let reader = BufReader::new(file);
        
        let header: IndexHeader = if self.config.enable_compression {
            use flate2::read::GzDecoder;
            let decoder = GzDecoder::new(reader);
            rmp_serde::decode::from_read(decoder)?
        } else {
            rmp_serde::decode::from_read(reader)?
        };
        
        
        if header.version != INDEX_FORMAT_VERSION {
            return Err(anyhow::anyhow!(
                "Incompatible index format version: {} (expected {})",
                header.version, INDEX_FORMAT_VERSION
            ));
        }
        
        Ok(header)
    }
    
    
    async fn save_vectors_mmap(&self, path: &PathBuf, vectors: &HashMap<VectorId, VectorEntry>) -> Result<()> {
        if vectors.is_empty() {
            return Ok(());
        }
        
        let dimension = vectors.values().next().unwrap().vector.len();
        let vector_size = dimension * std::mem::size_of::<f32>();
        let total_size = vectors.len() * (std::mem::size_of::<VectorId>() + vector_size);
        
        
        let file = OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(path)?;
        file.set_len(total_size as u64)?;
        
        
        let mut mmap = unsafe { MmapOptions::new().map_mut(&file)? };
        
        
        let mut offset = 0;
        for (id, entry) in vectors {
            
            let id_bytes = id.to_le_bytes();
            mmap[offset..offset + id_bytes.len()].copy_from_slice(&id_bytes);
            offset += id_bytes.len();
            
            
            for &value in &entry.vector {
                let value_bytes = value.to_le_bytes();
                mmap[offset..offset + value_bytes.len()].copy_from_slice(&value_bytes);
                offset += value_bytes.len();
            }
        }
        
        mmap.flush()?;
        Ok(())
    }
    
    
    async fn load_vectors_mmap(
        &self,
        vectors_path: &PathBuf,
        metadata_path: &PathBuf,
        header: &IndexHeader,
    ) -> Result<HashMap<VectorId, VectorEntry>> {
        
        let metadata_map = self.load_metadata_map(metadata_path).await?;
        
        
        let file = File::open(vectors_path)?;
        let mmap = unsafe { MmapOptions::new().map(&file)? };
        
        let mut vectors = HashMap::new();
        let vector_size = header.dimension * std::mem::size_of::<f32>();
        let entry_size = std::mem::size_of::<VectorId>() + vector_size;
        
        for i in 0..header.vector_count {
            let offset = i * entry_size;
            
            
            let id_bytes = &mmap[offset..offset + std::mem::size_of::<VectorId>()];
            let id = VectorId::from_le_bytes(id_bytes.try_into()?);
            
            
            let vector_offset = offset + std::mem::size_of::<VectorId>();
            let mut vector = Vec::with_capacity(header.dimension);
            
            for j in 0..header.dimension {
                let value_offset = vector_offset + j * std::mem::size_of::<f32>();
                let value_bytes = &mmap[value_offset..value_offset + std::mem::size_of::<f32>()];
                let value = f32::from_le_bytes(value_bytes.try_into()?);
                vector.push(value);
            }
            
            
            let metadata = metadata_map.get(&id)
                .cloned()
                .unwrap_or_default();
            
            vectors.insert(id, VectorEntry { id, vector, metadata });
        }
        
        Ok(vectors)
    }
    
    
    async fn save_metadata(&self, path: &PathBuf, vectors: &HashMap<VectorId, VectorEntry>) -> Result<()> {
        let metadata_map: HashMap<VectorId, VectorMetadata> = vectors
            .iter()
            .map(|(id, entry)| (*id, entry.metadata.clone()))
            .collect();
        
        let file = File::create(path)?;
        let mut writer = BufWriter::new(file);
        
        if self.config.enable_compression {
            use flate2::{Compression, write::GzEncoder};
            let mut encoder = GzEncoder::new(writer, Compression::default());
            rmp_serde::encode::write(&mut encoder, &metadata_map)?;
            encoder.finish()?;
        } else {
            rmp_serde::encode::write(&mut writer, &metadata_map)?;
        }
        
        Ok(())
    }
    
    
    async fn load_metadata_map(&self, path: &PathBuf) -> Result<HashMap<VectorId, VectorMetadata>> {
        let file = File::open(path)?;
        let reader = BufReader::new(file);
        
        let metadata_map: HashMap<VectorId, VectorMetadata> = if self.config.enable_compression {
            use flate2::read::GzDecoder;
            let decoder = GzDecoder::new(reader);
            rmp_serde::decode::from_read(decoder)?
        } else {
            rmp_serde::decode::from_read(reader)?
        };
        
        Ok(metadata_map)
    }
    
    
    async fn save_graph(&self, path: &PathBuf, _index: &HnswIndex) -> Result<()> {
        // For now, save a placeholder - full graph serialization would be complex
        let graph_data = HashMap::<String, String>::new();

        let file = File::create(path)?;
        let mut writer = BufWriter::new(file);
        
        if self.config.enable_compression {
            use flate2::{Compression, write::GzEncoder};
            let mut encoder = GzEncoder::new(writer, Compression::default());
            rmp_serde::encode::write(&mut encoder, &graph_data)?;
            encoder.finish()?;
        } else {
            rmp_serde::encode::write(&mut writer, &graph_data)?;
        }
        
        Ok(())
    }
    
    
    async fn load_graph(&self, _path: &PathBuf, header: &IndexHeader) -> Result<HnswIndex> {
        
        let config = HnswConfig::default();
        HnswIndex::new(config, header.distance_metric)
    }
    
    
    async fn create_backup(&self, path: &PathBuf) -> Result<()> {
        if !path.exists() {
            return Ok(());
        }
        
        let backup_path = path.with_extension(format!("backup.{}", 
            chrono::Utc::now().format("%Y%m%d_%H%M%S")));
        
        
        for extension in &["header", "vectors", "metadata", "graph"] {
            let src = path.with_extension(extension);
            let dst = backup_path.with_extension(extension);
            
            if src.exists() {
                std::fs::copy(&src, &dst)?;
            }
        }
        
        
        self.cleanup_old_backups(path).await?;
        
        Ok(())
    }
    
    
    async fn cleanup_old_backups(&self, path: &PathBuf) -> Result<()> {
        let parent_dir = path.parent().unwrap_or(Path::new("."));
        let base_name = path.file_stem().unwrap_or_default();
        
        let mut backup_files = Vec::new();
        
        for entry in std::fs::read_dir(parent_dir)? {
            let entry = entry?;
            let file_name = entry.file_name();
            let file_name_str = file_name.to_string_lossy();
            
            if file_name_str.starts_with(&format!("{}.backup.", base_name.to_string_lossy())) {
                backup_files.push((entry.path(), entry.metadata()?.modified()?));
            }
        }
        
        
        backup_files.sort_by(|a, b| b.1.cmp(&a.1));
        
        
        for (backup_path, _) in backup_files.iter().skip(self.config.backup_count) {
            if let Err(e) = std::fs::remove_file(backup_path) {
                tracing::warn!("Failed to remove old backup {:?}: {}", backup_path, e);
            }
        }
        
        Ok(())
    }
    
    
    fn calculate_checksum(&self, vectors: &HashMap<VectorId, VectorEntry>) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        
        
        let mut sorted_vectors: Vec<_> = vectors.iter().collect();
        sorted_vectors.sort_by_key(|(id, _)| *id);
        
        for (id, entry) in sorted_vectors {
            id.hash(&mut hasher);
            for &value in &entry.vector {
                value.to_bits().hash(&mut hasher);
            }
        }
        
        hasher.finish()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_index_persistence() {
        let temp_dir = TempDir::new().unwrap();
        let config = PersistenceConfig {
            base_path: temp_dir.path().to_path_buf(),
            enable_compression: true,
            backup_count: 2,
        };
        
        let persistence = IndexPersistence::new(config).unwrap();
        
        
        let mut vectors = HashMap::new();
        for i in 0..10 {
            vectors.insert(i, VectorEntry {
                id: i,
                vector: vec![i as f32; 4],
                metadata: VectorMetadata::default(),
            });
        }
        
        let index = HnswIndex::new(HnswConfig::default(), DistanceMetric::Cosine).unwrap();
        let save_path = temp_dir.path().join("test_index");
        
        
        persistence.save_index(&index, &vectors, &save_path).await.unwrap();
        
        
        let (loaded_index, loaded_vectors) = persistence.load_index(&save_path).await.unwrap();
        
        assert_eq!(loaded_vectors.len(), vectors.len());
        for (id, original) in &vectors {
            let loaded = &loaded_vectors[id];
            assert_eq!(loaded.vector, original.vector);
        }
    }
}
