use clap::Parser;
use semantic_search::{
    cli::{Cli, Commands, OutputFormat},
    config::Config,
    discovery::{<PERSON><PERSON><PERSON><PERSON>, ScanOptions},
    error::Result,
    search::{SearchEngine, SearchStats, SearchResult},
    indexing::{RealTimeIndexer, IndexingConfig, IndexingEvent},
};
use std::path::PathBuf;
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "semantic_search=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let cli = Cli::parse();
    let config = Config::load(&cli.config)?;

    match cli.command {
        Commands::Init { path, force } => {
            info!("Initializing semantic search index at: {}", path.display());
            println!("🚧 Phase 1: Init command placeholder");
            println!("   Path: {}", path.display());
            println!("   Force: {}", force);
            println!("✅ Index initialization planned (not yet implemented)");
        }
        Commands::Index { paths, watch, incremental } => {
            info!("📚 Starting document indexing for {} paths", paths.len());

            let search_engine = SearchEngine::new(&config).await?;

            // Warm up the ML system for better performance
            #[cfg(feature = "embeddings")]
            {
                info!("🔥 Warming up ML embeddings system...");
                if let Err(e) = search_engine.warmup().await {
                    warn!("Failed to warm up search engine: {}", e);
                }
            }

            // Discover documents from all paths
            use semantic_search::discovery::DocumentDiscovery;
            let discovery = DocumentDiscovery::new(&config.discovery);

            let mut all_documents = Vec::new();
            for path in &paths {
                let documents = discovery.discover_documents(path, true).await?;
                let doc_count = documents.len();
                all_documents.extend(documents);
                info!("📄 Found {} documents in {}", doc_count, path.display());
            }

            info!("📄 Total documents found: {}", all_documents.len());

            // Extract content with intelligent chunking and index with ML embeddings
            use semantic_search::extraction::{DocumentExtractor, ExtractionEngine, ExtractionConfig};

            // Create extraction config with intelligent chunking enabled
            let extraction_config = ExtractionConfig {
                max_file_size: config.extraction.max_file_size,
                enable_ocr: config.extraction.enable_ocr,
                ocr_languages: config.extraction.ocr_languages.clone(),
                preserve_formatting: config.extraction.preserve_formatting,
                extract_metadata: config.extraction.extract_metadata,
                chunk_size: config.extraction.chunk_size,
                timeout_seconds: config.extraction.timeout_seconds,
                encoding_detection: config.extraction.encoding_detection,
                fallback_to_binary: config.extraction.fallback_to_binary,
                chunking_config: Some(config.index.chunking.clone()),
            };

            let extraction_engine = ExtractionEngine::new(extraction_config);

            let mut indexed_docs = Vec::new();
            for doc_path in all_documents {
                match extraction_engine.extract_with_intelligent_chunking(&doc_path).await {
                    Ok(extracted) => {
                        info!("📄 Extracted {} with {} intelligent chunks",
                            doc_path.display(),
                            extracted.enhanced_chunks.len()
                        );

                        // Convert enhanced chunks to the format expected by search engine
                        for chunk in extracted.enhanced_chunks {
                            indexed_docs.push((doc_path.clone(), chunk.content));
                        }
                    }
                    Err(e) => {
                        warn!("Failed to extract content from {}: {}", doc_path.display(), e);

                        // Fallback to basic extraction
                        let extractor = DocumentExtractor::new(&config.extraction);
                        if let Ok(content) = extractor.extract_content(&doc_path).await {
                            indexed_docs.push((doc_path, content.text));
                        }
                    }
                }
            }

            // Bulk index with ML embeddings
            #[cfg(feature = "embeddings")]
            {
                let indexed_count = search_engine.bulk_index_documents(indexed_docs).await?;
                info!("🎉 Successfully indexed {} documents with ML embeddings", indexed_count);
            }

            #[cfg(not(feature = "embeddings"))]
            {
                let indexed_count = search_engine.bulk_index_documents_fallback(indexed_docs).await?;
                info!("✅ Successfully indexed {} documents (keyword-only)", indexed_count);
            }

            if watch {
                info!("👀 Watch mode enabled - starting real-time indexing...");
                return handle_index_with_watch(paths, incremental, config).await;
            }

            if incremental {
                info!("🔄 Incremental indexing enabled");
                println!("🚧 Incremental indexing not yet implemented");
            }
        }
        Commands::Search { query, limit, format, filter } => {
            info!("🔍 Starting semantic search for: '{}'", query);

            let search_engine = SearchEngine::new(&config).await?;

            // Warm up the search engine for better performance
            #[cfg(feature = "embeddings")]
            {
                info!("🔥 Warming up ML embeddings system...");
                if let Err(e) = search_engine.warmup().await {
                    warn!("Failed to warm up search engine: {}", e);
                }
            }

            let (results, stats) = search_engine.search(&query, limit, filter.as_deref()).await?;

            match format {
                OutputFormat::Json => {
                    let output = serde_json::json!({
                        "query": query,
                        "results": results,
                        "stats": stats
                    });
                    println!("{}", serde_json::to_string_pretty(&output)?);
                }
                OutputFormat::Table => {
                    print_search_results_with_stats(&results, &stats);
                }
            }
        }
        Commands::Serve { host, port } => {
            info!("Starting server on {}:{}", host, port);
            println!("🚧 Phase 1: Serve command placeholder");
            println!("   Host: {}", host);
            println!("   Port: {}", port);
            warn!("Server functionality will be implemented in later phases");
        }
        Commands::Chunking { args } => {
            use semantic_search::cli::chunking::handle_chunking_command;
            handle_chunking_command(args).await?;
        }
        Commands::Watch { paths, config_path } => {
            handle_watch_command(paths, config_path).await?;
        }
    }

    println!("\n📋 Configuration loaded:");
    println!("   Index path: {}", config.index.path.display());
    println!("   Chunk size: {}", config.index.chunk_size);
    println!("   Chunking strategy: {:?}", config.index.chunking.strategy);
    println!("   Max tokens: {}", config.index.chunking.max_tokens);
    println!("   Model: {}", config.embeddings.model_name);

    Ok(())
}

async fn handle_index_with_watch(paths: Vec<PathBuf>, incremental: bool, config: Config) -> Result<()> {
    info!("🚀 Starting integrated index + watch mode for {} paths", paths.len());

    // First, do initial indexing if not incremental
    if !incremental {
        info!("📚 Performing initial bulk indexing...");
        let search_engine = SearchEngine::new(&config).await?;

        // Warm up the ML system
        #[cfg(feature = "embeddings")]
        {
            info!("🔥 Warming up ML embeddings system...");
            if let Err(e) = search_engine.warmup().await {
                warn!("Failed to warm up search engine: {}", e);
            }
        }

        // Discover and index files
        let discovery_config = &config.discovery;
        let mut indexed_docs = Vec::new();

        for path in &paths {
            info!("🔍 Discovering files in: {}", path.display());
            let scan_options = ScanOptions {
                max_depth: discovery_config.max_depth,
                follow_symlinks: discovery_config.follow_symlinks,
                include_hidden: discovery_config.include_hidden,
                include_ignored: discovery_config.include_ignored,
                custom_ignores: discovery_config.ignore_patterns.clone(),
                include_patterns: discovery_config.include_patterns.clone(),
                exclude_patterns: discovery_config.exclude_patterns.clone(),
                max_file_size: discovery_config.max_file_size,
                parallel: discovery_config.parallel,
                show_progress: discovery_config.show_progress,
            };
            let scanner = FileScanner::new(scan_options)?;
            let (files, _stats) = scanner.scan(path)?;

            info!("📄 Found {} files to index", files.len());

            for file_info in files {
                if let Ok(content) = std::fs::read_to_string(&file_info.path) {
                    indexed_docs.push((file_info.path.clone(), content));

                    if indexed_docs.len() % 100 == 0 {
                        info!("📊 Processed {} files...", indexed_docs.len());
                    }
                }
            }
        }

        // Bulk index with ML embeddings
        #[cfg(feature = "embeddings")]
        {
            let indexed_count = search_engine.bulk_index_documents(indexed_docs).await?;
            info!("🎉 Initial indexing complete: {} documents with ML embeddings", indexed_count);
        }

        #[cfg(not(feature = "embeddings"))]
        {
            let indexed_count = search_engine.bulk_index_documents_fallback(indexed_docs).await?;
            info!("✅ Initial indexing complete: {} documents (keyword-only)", indexed_count);
        }
    }

    // Now start real-time watching
    info!("👀 Starting real-time file monitoring...");
    handle_watch_command(paths, None).await
}

async fn handle_watch_command(paths: Vec<PathBuf>, config_path: Option<PathBuf>) -> Result<()> {
    info!("🔍 Starting real-time file watching for {} paths", paths.len());

    // Load configuration
    let config_file = config_path.unwrap_or_else(|| PathBuf::from("config.toml"));
    let config = Config::load(&config_file)?;

    // Create indexing configuration
    let indexing_config = IndexingConfig {
        batch_size: 50,
        debounce_ms: 1000,
        max_concurrent_operations: 5,
        consistency_check_interval_ms: 300000, // 5 minutes
        enable_background_optimization: true,
        transaction_timeout_ms: 60000, // 1 minute
        max_retry_attempts: 3,
        checkpoint_interval_ms: 30000, // 30 seconds
    };

    // Initialize real-time indexer
    let (mut indexer, mut event_receiver, shutdown_sender) =
        RealTimeIndexer::new(&config.index.path, indexing_config, &config).await?;

    // Start event monitoring task
    let event_task = tokio::spawn(async move {
        while let Some(event) = event_receiver.recv().await {
            match event {
                IndexingEvent::Started { operation_id } => {
                    info!("🚀 Started operation: {}", operation_id);
                }
                IndexingEvent::Progress { operation_id, progress } => {
                    info!("📊 Operation {} progress: {:.1}%", operation_id, progress * 100.0);
                }
                IndexingEvent::Completed { operation_id, items_processed } => {
                    info!("✅ Completed operation {}: {} items processed", operation_id, items_processed);
                }
                IndexingEvent::Failed { operation_id, error } => {
                    warn!("❌ Failed operation {}: {}", operation_id, error);
                }
                IndexingEvent::BatchProcessed { batch_id, items } => {
                    info!("📦 Processed batch {}: {} items", batch_id, items);
                }
                IndexingEvent::ConsistencyCheckStarted => {
                    info!("🔍 Starting consistency check...");
                }
                IndexingEvent::ConsistencyCheckCompleted { issues_found } => {
                    if issues_found > 0 {
                        warn!("⚠️  Consistency check found {} issues", issues_found);
                    } else {
                        info!("✅ Consistency check passed");
                    }
                }
                IndexingEvent::BackgroundOptimizationStarted => {
                    info!("🔧 Starting background optimization...");
                }
                IndexingEvent::BackgroundOptimizationCompleted => {
                    info!("✅ Background optimization completed");
                }
            }
        }
    });

    // Setup graceful shutdown
    let shutdown_sender_clone = shutdown_sender.clone();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.expect("Failed to listen for ctrl+c");
        info!("🛑 Received shutdown signal, stopping indexer...");
        let _ = shutdown_sender_clone.send(true);
    });

    println!("🔍 Real-time indexing started!");
    println!("   Watching {} paths", paths.len());
    println!("   Index path: {}", config.index.path.display());
    println!("   Press Ctrl+C to stop");

    // Convert PathBuf to &Path for the indexer
    let watch_paths: Vec<&std::path::Path> = paths.iter().map(|p| p.as_path()).collect();

    // Start the indexer
    match indexer.start(watch_paths).await {
        Ok(_) => {
            info!("✅ Real-time indexer completed successfully");
        }
        Err(e) => {
            warn!("❌ Real-time indexer failed: {}", e);
            return Err(e);
        }
    }

    // Wait for event task to complete
    event_task.await?;

    println!("🏁 Real-time indexing stopped");
    Ok(())
}

fn print_search_results_with_stats(results: &[SearchResult], stats: &SearchStats) {
    println!("\n🔍 Search Results");
    println!("═══════════════════════════════════════════════════════════════");

    if results.is_empty() {
        println!("❌ No results found");
        return;
    }

    for (i, result) in results.iter().enumerate() {
        println!("\n📄 Result {} - Score: {:.3}", i + 1, result.score);
        println!("   📁 Path: {}", result.path.display());
        println!("   📝 Type: {}", result.file_type);

        if let Some(similarity) = result.similarity_score {
            println!("   🧠 Semantic: {:.3}", similarity);
        }
        if let Some(keyword) = result.keyword_score {
            println!("   🔤 Keyword: {:.3}", keyword);
        }

        if let Some(modified) = result.modified {
            println!("   📅 Modified: {}", modified.format("%Y-%m-%d %H:%M:%S"));
        }

        println!("   📖 Snippet: {}", result.snippet);

        if i < results.len() - 1 {
            println!("   ───────────────────────────────────────────────────────────");
        }
    }

    println!("\n📊 Performance Statistics");
    println!("═══════════════════════════════════════════════════════════════");
    println!("   📈 Total Results: {}", stats.total_documents);
    println!("   ⏱️  Total Time: {}ms", stats.search_time_ms);

    if stats.embedding_time_ms > 0 {
        println!("   🧠 Embedding Time: {}ms", stats.embedding_time_ms);
    }

    println!("   🔍 Retrieval Time: {}ms", stats.retrieval_time_ms);

    if stats.ranking_time_ms > 0 {
        println!("   📊 Ranking Time: {}ms", stats.ranking_time_ms);
    }

    let throughput = if stats.search_time_ms > 0 {
        (stats.total_documents as f64 / stats.search_time_ms as f64) * 1000.0
    } else {
        0.0
    };

    if throughput > 0.0 {
        println!("   🚀 Throughput: {:.1} results/sec", throughput);
    }
}
