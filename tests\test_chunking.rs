use semantic_search::chunking::{ChunkingEngine, ContentType};
use semantic_search::config::{ChunkingConfig, ChunkingStrategy};
use std::path::Path;
use tokio::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::init();

    println!("🧠 Testing Intelligent Document Chunking System\n");

    // Test different strategies
    let strategies = vec![
        ("Semantic", ChunkingStrategy::Semantic),
        ("Code-Aware", ChunkingStrategy::CodeAware),
        ("Sliding Window", ChunkingStrategy::SlidingWindow),
        ("Adaptive", ChunkingStrategy::Adaptive),
    ];

    let test_files = vec![
        ("simple_test.md", "Markdown document"),
        ("test_content.rs", "Rust source code"),
    ];

    for (file_path, description) in test_files {
        println!("📄 Testing file: {} ({})", file_path, description);
        
        if !Path::new(file_path).exists() {
            println!("   ⚠️  File not found, skipping...\n");
            continue;
        }

        let content = fs::read_to_string(file_path).await?;
        println!("   📊 Content: {} characters, {} words", 
            content.len(), 
            content.split_whitespace().count()
        );

        for (strategy_name, strategy) in &strategies {
            let config = ChunkingConfig {
                strategy: strategy.clone(),
                max_tokens: 256,
                overlap_tokens: 25,
                min_chunk_size: 50,
                max_chunk_size: 512,
                preserve_sentence_boundaries: true,
                preserve_code_boundaries: true,
                enable_semantic_chunking: true,
                semantic_similarity_threshold: 0.8,
                code_languages: vec![
                    "rust".to_string(),
                    "python".to_string(),
                    "javascript".to_string(),
                    "markdown".to_string(),
                ],
            };

            let chunking_engine = ChunkingEngine::new(config);
            
            match chunking_engine.chunk_document(&content, Path::new(file_path), None).await {
                Ok(result) => {
                    println!("   🔧 {} Strategy:", strategy_name);
                    println!("      Chunks: {}", result.metadata.total_chunks);
                    println!("      Avg size: {:.1} tokens", result.metadata.average_chunk_size);
                    println!("      Variance: {:.2}", result.metadata.size_variance);
                    println!("      Boundary score: {:.1}%", result.metadata.boundary_preservation_score * 100.0);
                    println!("      Processing: {}ms", result.metadata.processing_time_ms);
                    
                    // Show first chunk as example
                    if let Some(first_chunk) = result.chunks.first() {
                        let preview = first_chunk.content.chars().take(100).collect::<String>()
                            .replace('\n', " ");
                        println!("      Preview: {}...", preview);
                    }
                    println!();
                }
                Err(e) => {
                    println!("   ❌ {} Strategy failed: {}", strategy_name, e);
                }
            }
        }
        
        println!("   ✅ Completed testing {}\n", file_path);
    }

    // Test streaming chunking
    println!("🔄 Testing Streaming Chunking");
    
    if Path::new("simple_test.md").exists() {
        let content = fs::read_to_string("simple_test.md").await?;
        
        let config = ChunkingConfig {
            strategy: ChunkingStrategy::Adaptive,
            max_tokens: 200,
            overlap_tokens: 20,
            min_chunk_size: 50,
            max_chunk_size: 400,
            preserve_sentence_boundaries: true,
            preserve_code_boundaries: true,
            enable_semantic_chunking: true,
            semantic_similarity_threshold: 0.8,
            code_languages: vec!["markdown".to_string()],
        };

        let chunking_engine = ChunkingEngine::new(config);
        
        let mut chunk_count = 0;
        let metadata = chunking_engine.chunk_streaming(
            &content,
            Path::new("simple_test.md"),
            None,
            |chunk| {
                chunk_count += 1;
                println!("   📦 Chunk {}: {} tokens, type: {:?}", 
                    chunk_count, 
                    chunk.metadata.token_count,
                    chunk.chunk_type
                );
                
                if chunk_count >= 3 {
                    println!("   ... (stopping after 3 chunks for demo)");
                    return false; // Stop after 3 chunks for demo
                }
                true
            },
        ).await?;
        
        println!("   📊 Streaming metadata: {} total chunks, {}ms processing", 
            metadata.total_chunks, 
            metadata.processing_time_ms
        );
    }

    println!("\n🎉 Chunking system test completed successfully!");
    Ok(())
}
