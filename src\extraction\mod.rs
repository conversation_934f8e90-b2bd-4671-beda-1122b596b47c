pub mod plain_text;

#[cfg(feature = "pdf")]
pub mod pdf;

#[cfg(feature = "ocr")]
pub mod image;

use crate::error::Result;
use crate::chunking::{ChunkingEngine, EnhancedTextChunk, ContentType};
use crate::config::ChunkingConfig;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::collections::HashMap;
use tracing::{info, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedText {
    pub content: String,
    pub metadata: TextMetadata,
    pub chunks: Vec<TextChunk>,
}

#[derive(Debug, Clone)]
pub struct ExtractedTextWithChunks {
    pub content: String,
    pub metadata: TextMetadata,
    pub enhanced_chunks: Vec<EnhancedTextChunk>,
    pub chunking_metadata: crate::chunking::DocumentChunkingMetadata,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TextMetadata {
    pub file_path: String,
    pub file_size: u64,
    pub extraction_method: String,
    pub language: Option<String>,
    pub encoding: Option<String>,
    pub page_count: Option<usize>,
    pub word_count: usize,
    pub char_count: usize,
    pub extraction_time_ms: u64,
    pub confidence: Option<f32>, 
    pub properties: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextChunk {
    pub content: String,
    pub chunk_type: ChunkType,
    pub position: ChunkPosition,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChunkType {
    Paragraph,
    Heading(u8), 
    Code(String), 
    Table,
    List,
    Quote,
    Image, 
    Footer,
    Header,
    Raw,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkPosition {
    pub page: Option<usize>,
    pub line: Option<usize>,
    pub column: Option<usize>,
    pub offset: Option<usize>,
}


pub enum ExtractorType {
    PlainText(plain_text::PlainTextExtractor),
    #[cfg(feature = "pdf")]
    PDF(pdf::PDFExtractor),
    #[cfg(feature = "ocr")]
    Image(image::ImageExtractor),
}

impl ExtractorType {
    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        match self {
            ExtractorType::PlainText(e) => e.extract(file_path).await,
            #[cfg(feature = "pdf")]
            ExtractorType::PDF(e) => e.extract(file_path).await,
            #[cfg(feature = "ocr")]
            ExtractorType::Image(e) => e.extract(file_path).await,
        }
    }

    pub async fn extract_streaming<F>(&self, file_path: &Path, chunk_callback: F) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        match self {
            ExtractorType::PlainText(e) => e.extract_streaming(file_path, chunk_callback).await,
            #[cfg(feature = "pdf")]
            ExtractorType::PDF(e) => e.extract_streaming(file_path, chunk_callback).await,
            #[cfg(feature = "ocr")]
            ExtractorType::Image(e) => e.extract_streaming(file_path, chunk_callback).await,
        }
    }

    pub fn supports_file(&self, file_path: &Path) -> bool {
        match self {
            ExtractorType::PlainText(e) => e.supports_file(file_path),
            #[cfg(feature = "pdf")]
            ExtractorType::PDF(e) => e.supports_file(file_path),
            #[cfg(feature = "ocr")]
            ExtractorType::Image(e) => e.supports_file(file_path),
        }
    }

    pub fn get_supported_extensions(&self) -> Vec<&'static str> {
        match self {
            ExtractorType::PlainText(e) => e.get_supported_extensions(),
            #[cfg(feature = "pdf")]
            ExtractorType::PDF(e) => e.get_supported_extensions(),
            #[cfg(feature = "ocr")]
            ExtractorType::Image(e) => e.get_supported_extensions(),
        }
    }

    pub fn get_extractor_name(&self) -> &'static str {
        match self {
            ExtractorType::PlainText(e) => e.get_extractor_name(),
            #[cfg(feature = "pdf")]
            ExtractorType::PDF(e) => e.get_extractor_name(),
            #[cfg(feature = "ocr")]
            ExtractorType::Image(e) => e.get_extractor_name(),
        }
    }
}


pub trait TextExtractor: Send + Sync {
    fn supports_file(&self, file_path: &Path) -> bool;
    fn get_supported_extensions(&self) -> Vec<&'static str>;
    fn get_extractor_name(&self) -> &'static str;
}

pub struct ExtractionEngine {
    extractors: Vec<ExtractorType>,
    config: ExtractionConfig,
    chunking_engine: Option<ChunkingEngine>,
}

#[derive(Debug, Clone)]
pub struct ExtractionConfig {
    pub max_file_size: u64,
    pub enable_ocr: bool,
    pub ocr_languages: Vec<String>,
    pub preserve_formatting: bool,
    pub extract_metadata: bool,
    pub chunk_size: usize,
    pub timeout_seconds: u64,
    pub encoding_detection: bool,
    pub fallback_to_binary: bool,
    pub chunking_config: Option<ChunkingConfig>,
}

impl Default for ExtractionConfig {
    fn default() -> Self {
        Self {
            max_file_size: 100 * 1024 * 1024,
            enable_ocr: false,
            ocr_languages: vec!["eng".to_string()],
            preserve_formatting: true,
            extract_metadata: true,
            chunk_size: 1000,
            timeout_seconds: 30,
            encoding_detection: true,
            fallback_to_binary: false,
            chunking_config: None,
        }
    }
}

impl ExtractionEngine {
    pub fn new(config: ExtractionConfig) -> Self {
        let mut extractors = Vec::new();


        #[cfg(feature = "pdf")]
        extractors.push(ExtractorType::PDF(pdf::PDFExtractor::new()));

        extractors.push(ExtractorType::PlainText(plain_text::PlainTextExtractor::new()));

        #[cfg(feature = "ocr")]
        if config.enable_ocr {
            extractors.push(ExtractorType::Image(image::ImageExtractor::new(config.ocr_languages.clone())));
        }

        let chunking_engine = config.chunking_config.as_ref().map(|c| ChunkingEngine::new(c.clone()));

        Self {
            extractors,
            config,
            chunking_engine,
        }
    }

    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        let start_time = std::time::Instant::now();

        
        if let Ok(metadata) = std::fs::metadata(file_path) {
            if metadata.len() > self.config.max_file_size {
                return Err(anyhow::anyhow!(
                    "File too large: {} bytes (max: {})",
                    metadata.len(),
                    self.config.max_file_size
                ));
            }
        }

        
        let extractor = self.find_extractor(file_path)?;

        info!("Extracting text from {} using {}",
              file_path.display(),
              extractor.get_extractor_name());

        
        let extraction_future = extractor.extract(file_path);
        let result = tokio::time::timeout(
            std::time::Duration::from_secs(self.config.timeout_seconds),
            extraction_future
        ).await;

        match result {
            Ok(Ok(mut extracted)) => {
                extracted.metadata.extraction_time_ms = start_time.elapsed().as_millis() as u64;
                Ok(extracted)
            }
            Ok(Err(e)) => {
                warn!("Extraction failed for {}: {}", file_path.display(), e);
                Err(e)
            }
            Err(_) => {
                warn!("Extraction timeout for {}", file_path.display());
                Err(anyhow::anyhow!("Extraction timeout"))
            }
        }
    }

    pub async fn extract_streaming<F>(
        &self,
        file_path: &Path,
        chunk_callback: F
    ) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        let start_time = std::time::Instant::now();
        let extractor = self.find_extractor(file_path)?;

        info!("Streaming extraction from {} using {}",
              file_path.display(),
              extractor.get_extractor_name());

        let extraction_future = extractor.extract_streaming(file_path, chunk_callback);
        let result = tokio::time::timeout(
            std::time::Duration::from_secs(self.config.timeout_seconds),
            extraction_future
        ).await;

        match result {
            Ok(Ok(mut metadata)) => {
                metadata.extraction_time_ms = start_time.elapsed().as_millis() as u64;
                Ok(metadata)
            }
            Ok(Err(e)) => {
                warn!("Streaming extraction failed for {}: {}", file_path.display(), e);
                Err(e)
            }
            Err(_) => {
                warn!("Streaming extraction timeout for {}", file_path.display());
                Err(anyhow::anyhow!("Streaming extraction timeout"))
            }
        }
    }

    fn find_extractor(&self, file_path: &Path) -> Result<&ExtractorType> {
        for extractor in &self.extractors {
            if extractor.supports_file(file_path) {
                return Ok(extractor);
            }
        }

        Err(anyhow::anyhow!(
            "No suitable extractor found for file: {}",
            file_path.display()
        ))
    }

    pub fn get_supported_extensions(&self) -> Vec<&'static str> {
        let mut extensions = Vec::new();
        for extractor in &self.extractors {
            extensions.extend(extractor.get_supported_extensions());
        }
        extensions.sort();
        extensions.dedup();
        extensions
    }

    pub fn get_extractor_info(&self) -> Vec<(String, Vec<&'static str>)> {
        self.extractors
            .iter()
            .map(|e| (e.get_extractor_name().to_string(), e.get_supported_extensions()))
            .collect()
    }

    pub async fn extract_with_intelligent_chunking(&self, file_path: &Path) -> Result<ExtractedTextWithChunks> {
        let extracted = self.extract(file_path).await?;

        if let Some(chunking_engine) = &self.chunking_engine {
            let chunked_doc = chunking_engine.chunk_document(
                &extracted.content,
                file_path,
                None,
            ).await?;

            Ok(ExtractedTextWithChunks {
                content: extracted.content,
                metadata: extracted.metadata,
                enhanced_chunks: chunked_doc.chunks,
                chunking_metadata: chunked_doc.metadata,
            })
        } else {
            let enhanced_chunks = extracted.chunks.into_iter().map(|chunk| {
                EnhancedTextChunk {
                    content: chunk.content.clone(),
                    chunk_type: chunk.chunk_type,
                    position: chunk.position,
                    metadata: crate::chunking::ChunkMetadata {
                        source_file: file_path.to_string_lossy().to_string(),
                        chunk_index: 0,
                        total_chunks: 1,
                        token_count: crate::chunking::estimate_token_count(&chunk.content),
                        char_count: chunk.content.chars().count(),
                        word_count: crate::chunking::count_words(&chunk.content),
                        language: None,
                        content_type: ContentType::Unknown,
                        semantic_score: None,
                        overlap_start: None,
                        overlap_end: None,
                        context_before: None,
                        context_after: None,
                        properties: chunk.metadata,
                    },
                    boundaries: crate::chunking::ChunkBoundaries {
                        starts_at_sentence: false,
                        ends_at_sentence: false,
                        starts_at_paragraph: false,
                        ends_at_paragraph: false,
                        starts_at_code_block: false,
                        ends_at_code_block: false,
                        semantic_boundary_score: None,
                    },
                }
            }).collect();

            Ok(ExtractedTextWithChunks {
                content: extracted.content,
                metadata: extracted.metadata,
                enhanced_chunks,
                chunking_metadata: crate::chunking::DocumentChunkingMetadata {
                    total_chunks: 1,
                    total_tokens: 0,
                    total_chars: 0,
                    chunking_strategy: "legacy".to_string(),
                    processing_time_ms: 0,
                    overlap_ratio: 0.0,
                    average_chunk_size: 0.0,
                    size_variance: 0.0,
                    boundary_preservation_score: 0.0,
                },
            })
        }
    }

    pub async fn extract_streaming_with_chunking<F>(
        &self,
        file_path: &Path,
        chunk_callback: F,
    ) -> Result<crate::chunking::DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync,
    {
        if let Some(chunking_engine) = &self.chunking_engine {
            let extracted = self.extract(file_path).await?;
            chunking_engine.chunk_streaming(
                &extracted.content,
                file_path,
                None,
                chunk_callback,
            ).await
        } else {
            let metadata = self.extract_streaming(file_path, |_| true).await?;
            Ok(crate::chunking::DocumentChunkingMetadata {
                total_chunks: 1,
                total_tokens: metadata.word_count,
                total_chars: metadata.char_count,
                chunking_strategy: "legacy".to_string(),
                processing_time_ms: metadata.extraction_time_ms,
                overlap_ratio: 0.0,
                average_chunk_size: metadata.word_count as f32,
                size_variance: 0.0,
                boundary_preservation_score: 0.0,
            })
        }
    }
}


pub fn detect_encoding(bytes: &[u8]) -> Option<String> {
    let (encoding, _) = encoding_rs::Encoding::for_bom(bytes)
        .unwrap_or((encoding_rs::UTF_8, 0));

    if encoding == encoding_rs::UTF_8 {
        Some("UTF-8".to_string())
    } else {
        Some(encoding.name().to_string())
    }
}

pub fn count_words(text: &str) -> usize {
    text.split_whitespace().count()
}

pub fn create_text_chunks(text: &str, chunk_size: usize) -> Vec<TextChunk> {
    let mut chunks = Vec::new();
    let mut offset = 0;
    
    for (line_num, line) in text.lines().enumerate() {
        if line.trim().is_empty() {
            offset += line.len() + 1; 
            continue;
        }
        
        
        if line.len() > chunk_size {
            for (i, chunk_text) in line.chars()
                .collect::<Vec<_>>()
                .chunks(chunk_size)
                .enumerate() 
            {
                let chunk_content: String = chunk_text.iter().collect();
                chunks.push(TextChunk {
                    content: chunk_content,
                    chunk_type: ChunkType::Raw,
                    position: ChunkPosition {
                        page: None,
                        line: Some(line_num),
                        column: Some(i * chunk_size),
                        offset: Some(offset),
                    },
                    metadata: HashMap::new(),
                });
                offset += chunk_text.len();
            }
        } else {
            chunks.push(TextChunk {
                content: line.to_string(),
                chunk_type: ChunkType::Paragraph,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata: HashMap::new(),
            });
            offset += line.len() + 1; 
        }
    }
    
    chunks
}


pub struct DocumentExtractor {
    engine: ExtractionEngine,
}

impl DocumentExtractor {
    pub fn new(config: &crate::config::ExtractorConfig) -> Self {
        let extraction_config = ExtractionConfig {
            max_file_size: config.max_file_size,
            enable_ocr: config.enable_ocr,
            ocr_languages: config.ocr_languages.clone(),
            preserve_formatting: config.preserve_formatting,
            extract_metadata: config.extract_metadata,
            chunk_size: config.chunk_size,
            timeout_seconds: config.timeout_seconds,
            encoding_detection: config.encoding_detection,
            fallback_to_binary: config.fallback_to_binary,
            chunking_config: None,
        };

        let engine = ExtractionEngine::new(extraction_config);
        Self { engine }
    }

    pub async fn extract_content(&self, path: &std::path::Path) -> Result<ExtractedContent> {
        let extracted = self.engine.extract(path).await?;
        Ok(ExtractedContent {
            text: extracted.content,
            metadata: extracted.metadata,
        })
    }
}

#[derive(Debug, Clone)]
pub struct ExtractedContent {
    pub text: String,
    pub metadata: TextMetadata,
}
