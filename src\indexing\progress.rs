use crate::indexing::{IndexingEvent, IndexingEventSender};
use atomic_float::AtomicF64;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IndexingProgress {
    pub operation_id: String,
    pub operation_type: String,
    pub current_progress: f64,
    pub total_items: usize,
    pub processed_items: usize,
    pub items_per_second: f64,
    pub estimated_remaining_ms: u64,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub last_updated: chrono::DateTime<chrono::Utc>,
    pub status: ProgressStatus,
    pub current_item: Option<String>,
    pub errors: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ProgressStatus {
    Starting,
    InProgress,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ProgressEvent {
    OperationStarted {
        operation_id: String,
        operation_type: String,
        total_items: usize,
    },
    ProgressUpdate {
        operation_id: String,
        processed_items: usize,
        current_item: Option<String>,
    },
    OperationCompleted {
        operation_id: String,
        total_processed: usize,
        duration_ms: u64,
    },
    OperationFailed {
        operation_id: String,
        error: String,
        processed_items: usize,
    },
    BatchCompleted {
        operation_id: String,
        batch_size: usize,
        batch_duration_ms: u64,
    },
}

pub struct ProgressReporter {
    event_sender: IndexingEventSender,
    active_operations: Arc<RwLock<HashMap<String, OperationProgress>>>,
    global_stats: Arc<GlobalStats>,
}

struct OperationProgress {
    operation_id: String,
    operation_type: String,
    total_items: AtomicUsize,
    processed_items: AtomicUsize,
    started_at: Instant,
    last_update: Arc<RwLock<Instant>>,
    current_item: Arc<RwLock<Option<String>>>,
    status: Arc<RwLock<ProgressStatus>>,
    errors: Arc<RwLock<Vec<String>>>,
    throughput_tracker: Arc<ThroughputTracker>,
}

#[derive(Debug, Default)]
struct GlobalStats {
    total_operations: AtomicU64,
    completed_operations: AtomicU64,
    failed_operations: AtomicU64,
    total_items_processed: AtomicU64,
    total_bytes_processed: AtomicU64,
    average_throughput: AtomicF64,
}

struct ThroughputTracker {
    samples: Arc<RwLock<Vec<(Instant, usize)>>>,
    window_size: Duration,
}

impl ThroughputTracker {
    fn new(window_size: Duration) -> Self {
        Self {
            samples: Arc::new(RwLock::new(Vec::new())),
            window_size,
        }
    }

    async fn record_progress(&self, processed_items: usize) {
        let now = Instant::now();
        let mut samples = self.samples.write().await;
        
        // Add new sample
        samples.push((now, processed_items));
        
        // Remove old samples outside the window
        let cutoff = now - self.window_size;
        samples.retain(|(timestamp, _)| *timestamp > cutoff);
    }

    async fn calculate_throughput(&self) -> f64 {
        let samples = self.samples.read().await;
        
        if samples.len() < 2 {
            return 0.0;
        }

        let first = &samples[0];
        let last = &samples[samples.len() - 1];
        
        let duration = last.0.duration_since(first.0).as_secs_f64();
        let items_processed = last.1.saturating_sub(first.1) as f64;
        
        if duration > 0.0 {
            items_processed / duration
        } else {
            0.0
        }
    }
}

impl ProgressReporter {
    pub fn new(event_sender: IndexingEventSender) -> Self {
        Self {
            event_sender,
            active_operations: Arc::new(RwLock::new(HashMap::new())),
            global_stats: Arc::new(GlobalStats::default()),
        }
    }

    pub async fn start_operation(
        &self,
        operation_id: String,
        operation_type: String,
        total_items: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let operation_progress = OperationProgress {
            operation_id: operation_id.clone(),
            operation_type: operation_type.clone(),
            total_items: AtomicUsize::new(total_items),
            processed_items: AtomicUsize::new(0),
            started_at: Instant::now(),
            last_update: Arc::new(RwLock::new(Instant::now())),
            current_item: Arc::new(RwLock::new(None)),
            status: Arc::new(RwLock::new(ProgressStatus::Starting)),
            errors: Arc::new(RwLock::new(Vec::new())),
            throughput_tracker: Arc::new(ThroughputTracker::new(Duration::from_secs(30))),
        };

        {
            let mut operations = self.active_operations.write().await;
            operations.insert(operation_id.clone(), operation_progress);
        }

        self.global_stats.total_operations.fetch_add(1, Ordering::Relaxed);

        let _ = self.event_sender.send(IndexingEvent::Started { operation_id: operation_id.clone() });

        // Update status to in progress
        {
            let operations = self.active_operations.read().await;
            if let Some(op) = operations.get(&operation_id) {
                let mut status = op.status.write().await;
                *status = ProgressStatus::InProgress;
            }
        }

        Ok(())
    }

    pub async fn update_progress(
        &self,
        operation_id: &str,
        processed_items: usize,
        current_item: Option<String>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let operations = self.active_operations.read().await;
        
        if let Some(operation) = operations.get(operation_id) {
            operation.processed_items.store(processed_items, Ordering::Relaxed);
            
            {
                let mut last_update = operation.last_update.write().await;
                *last_update = Instant::now();
            }

            if let Some(item) = current_item.clone() {
                let mut current = operation.current_item.write().await;
                *current = Some(item);
            }

            // Record throughput
            operation.throughput_tracker.record_progress(processed_items).await;

            // Calculate progress percentage
            let total = operation.total_items.load(Ordering::Relaxed);
            let progress = if total > 0 {
                processed_items as f64 / total as f64
            } else {
                0.0
            };

            let _ = self.event_sender.send(IndexingEvent::Progress {
                operation_id: operation_id.to_string(),
                progress,
            });
        }

        Ok(())
    }

    pub async fn complete_operation(
        &self,
        operation_id: &str,
        success: bool,
        error: Option<String>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut operations = self.active_operations.write().await;
        
        if let Some(operation) = operations.remove(operation_id) {
            let processed_items = operation.processed_items.load(Ordering::Relaxed);
            let duration = operation.started_at.elapsed();

            if success {
                self.global_stats.completed_operations.fetch_add(1, Ordering::Relaxed);
                self.global_stats.total_items_processed.fetch_add(processed_items as u64, Ordering::Relaxed);

                let _ = self.event_sender.send(IndexingEvent::Completed {
                    operation_id: operation_id.to_string(),
                    items_processed: processed_items,
                });
            } else {
                self.global_stats.failed_operations.fetch_add(1, Ordering::Relaxed);

                let _ = self.event_sender.send(IndexingEvent::Failed {
                    operation_id: operation_id.to_string(),
                    error: error.unwrap_or_else(|| "Unknown error".to_string()),
                });
            }

            // Update global average throughput
            let throughput = operation.throughput_tracker.calculate_throughput().await;
            self.update_global_throughput(throughput).await;
        }

        Ok(())
    }

    async fn update_global_throughput(&self, new_throughput: f64) {
        // Simple exponential moving average
        let alpha = 0.1;
        let current = self.global_stats.average_throughput.load(Ordering::Relaxed);
        let updated = current * (1.0 - alpha) + new_throughput * alpha;
        self.global_stats.average_throughput.store(updated, Ordering::Relaxed);
    }

    pub async fn get_operation_progress(&self, operation_id: &str) -> Option<IndexingProgress> {
        let operations = self.active_operations.read().await;
        
        if let Some(operation) = operations.get(operation_id) {
            let total_items = operation.total_items.load(Ordering::Relaxed);
            let processed_items = operation.processed_items.load(Ordering::Relaxed);
            let current_progress = if total_items > 0 {
                processed_items as f64 / total_items as f64
            } else {
                0.0
            };

            let throughput = operation.throughput_tracker.calculate_throughput().await;
            let remaining_items = total_items.saturating_sub(processed_items);
            let estimated_remaining_ms = if throughput > 0.0 {
                ((remaining_items as f64 / throughput) * 1000.0) as u64
            } else {
                0
            };

            let current_item = operation.current_item.read().await.clone();
            let status = operation.status.read().await.clone();
            let errors = operation.errors.read().await.clone();

            Some(IndexingProgress {
                operation_id: operation_id.to_string(),
                operation_type: operation.operation_type.clone(),
                current_progress,
                total_items,
                processed_items,
                items_per_second: throughput,
                estimated_remaining_ms,
                started_at: chrono::DateTime::from(
                    std::time::UNIX_EPOCH + operation.started_at.elapsed()
                ),
                last_updated: chrono::Utc::now(),
                status,
                current_item,
                errors,
            })
        } else {
            None
        }
    }

    pub async fn get_all_operations(&self) -> Vec<IndexingProgress> {
        let operations = self.active_operations.read().await;
        let mut progress_list = Vec::new();

        for operation_id in operations.keys() {
            if let Some(progress) = self.get_operation_progress(operation_id).await {
                progress_list.push(progress);
            }
        }

        progress_list
    }

    pub async fn cancel_operation(&self, operation_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let operations = self.active_operations.read().await;
        
        if let Some(operation) = operations.get(operation_id) {
            let mut status = operation.status.write().await;
            *status = ProgressStatus::Cancelled;
        }

        Ok(())
    }

    pub async fn add_error(&self, operation_id: &str, error: String) {
        let operations = self.active_operations.read().await;
        
        if let Some(operation) = operations.get(operation_id) {
            let mut errors = operation.errors.write().await;
            errors.push(error);
        }
    }

    pub fn get_global_stats(&self) -> GlobalStatsSnapshot {
        GlobalStatsSnapshot {
            total_operations: self.global_stats.total_operations.load(Ordering::Relaxed),
            completed_operations: self.global_stats.completed_operations.load(Ordering::Relaxed),
            failed_operations: self.global_stats.failed_operations.load(Ordering::Relaxed),
            total_items_processed: self.global_stats.total_items_processed.load(Ordering::Relaxed),
            total_bytes_processed: self.global_stats.total_bytes_processed.load(Ordering::Relaxed),
            average_throughput: self.global_stats.average_throughput.load(Ordering::Relaxed),
        }
    }

    pub async fn cleanup_completed_operations(&self) {
        let mut operations = self.active_operations.write().await;
        
        operations.retain(|_, operation| {
            let status = operation.status.try_read();
            match status {
                Ok(status) => !matches!(*status, ProgressStatus::Completed | ProgressStatus::Failed | ProgressStatus::Cancelled),
                Err(_) => true, // Keep if we can't read the status
            }
        });
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalStatsSnapshot {
    pub total_operations: u64,
    pub completed_operations: u64,
    pub failed_operations: u64,
    pub total_items_processed: u64,
    pub total_bytes_processed: u64,
    pub average_throughput: f64,
}
