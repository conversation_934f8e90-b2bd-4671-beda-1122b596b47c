[index]
path = ".semantic_search"
chunk_size = 512
chunk_overlap = 50
max_file_size = 104857600  # 100MB
ignore_patterns = ["*.git*", "node_modules", "target", "*.log", "*.tmp"]
include_patterns = ["*.rs", "*.py", "*.js", "*.ts", "*.md", "*.txt", "*.json", "*.yaml", "*.yml", "*.toml", "*.pdf", "*.docx"]
watch_debounce_ms = 500

[index.chunking]
strategy = "Adaptive"
max_tokens = 512
overlap_tokens = 50
min_chunk_size = 100
max_chunk_size = 2048
preserve_sentence_boundaries = true
preserve_code_boundaries = true
enable_semantic_chunking = true
semantic_similarity_threshold = 0.8
code_languages = ["rust", "python", "javascript", "typescript", "json", "yaml", "toml", "markdown"]

[embeddings]
model_name = "sentence-transformers/all-MiniLM-L6-v2"
dimension = 384
batch_size = 32
device = "cpu"
cache_size = 1000

[search]
similarity_threshold = 0.3
max_results = 50
snippet_length = 200
snippet_context = 50
enable_hybrid_search = true
keyword_weight = 0.3
semantic_weight = 0.7

[server]
host = "127.0.0.1"
port = 8080
cors_origins = ["*"]
rate_limit_requests = 100
rate_limit_window_seconds = 60
max_request_size = 10485760  # 10MB

[extraction]
max_file_size = 104857600  # 100MB
enable_ocr = false
ocr_languages = ["eng"]
preserve_formatting = true
extract_metadata = true
chunk_size = 1000
timeout_seconds = 30
encoding_detection = true
fallback_to_binary = false

[discovery]
max_depth = 10
follow_symlinks = false
include_hidden = false
include_ignored = false
ignore_patterns = ["*.git*", "node_modules", "target", "*.log", "*.tmp"]
include_patterns = ["*.rs", "*.py", "*.js", "*.ts", "*.md", "*.txt", "*.json", "*.yaml", "*.yml", "*.toml"]
exclude_patterns = ["*.exe", "*.dll", "*.so", "*.dylib"]
max_file_size = 104857600  # 100MB
parallel = true
show_progress = true

[extractors]
enable_pdf = true
enable_docx = true
enable_images = true
enable_ocr = false
max_image_size = 52428800  # 50MB
supported_extensions = ["txt", "md", "rs", "py", "js", "ts", "json", "yaml", "yml", "toml", "pdf", "docx", "png", "jpg", "jpeg"]

[storage]
backend = "sled"
path = ".semantic_search/storage"
compression_level = 6
cache_size_mb = 100
sync_interval_ms = 5000
enable_encryption = false
backup_enabled = false
backup_interval_hours = 24
max_backup_files = 7

[vector_search]
index_type = "hnsw"
distance_metric = "cosine"
enable_persistence = true
persistence_path = ".semantic_search/vectors"
enable_compression = true

[vector_search.hnsw]
m = 16
ef_construction = 200
ef_search = 100
max_connections = 32
level_multiplier = 1.0

[vector_search.cache]
enabled = true
capacity = 10000
ttl_seconds = 3600
