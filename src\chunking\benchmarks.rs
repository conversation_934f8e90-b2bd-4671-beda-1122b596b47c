use super::{
    Document<PERSON>hunker, ChunkedDocument, ContentType, DocumentChunkingMetadata,
    ChunkerType, semantic::SemanticChunker, code_aware::CodeAwareChunker,
    sliding_window::SlidingWindowChunker, adaptive::AdaptiveChunker,
};
use crate::config::ChunkingConfig;
use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::time::Instant;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkingBenchmark {
    pub strategy_name: String,
    pub content_type: String,
    pub content_size: usize,
    pub processing_time_ms: u64,
    pub total_chunks: usize,
    pub average_chunk_size: f32,
    pub size_variance: f32,
    pub overlap_ratio: f32,
    pub boundary_preservation_score: f32,
    pub memory_usage_mb: f32,
    pub throughput_chars_per_ms: f32,
    pub quality_metrics: QualityMetrics,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct QualityMetrics {
    pub semantic_coherence_score: f32,
    pub context_preservation_score: f32,
    pub boundary_accuracy_score: f32,
    pub size_consistency_score: f32,
    pub overlap_efficiency_score: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkSuite {
    pub benchmarks: Vec<ChunkingBenchmark>,
    pub summary: BenchmarkSummary,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkSummary {
    pub best_overall_strategy: String,
    pub best_for_code: String,
    pub best_for_natural_language: String,
    pub best_for_mixed_content: String,
    pub performance_rankings: Vec<StrategyRanking>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyRanking {
    pub strategy: String,
    pub overall_score: f32,
    pub performance_score: f32,
    pub quality_score: f32,
    pub consistency_score: f32,
}

pub struct ChunkingBenchmarkRunner {
    chunkers: HashMap<String, ChunkerType>,
}

impl ChunkingBenchmarkRunner {
    pub fn new() -> Self {
        let mut chunkers: HashMap<String, ChunkerType> = HashMap::new();

        chunkers.insert("semantic".to_string(), ChunkerType::Semantic(SemanticChunker::new()));
        chunkers.insert("code_aware".to_string(), ChunkerType::CodeAware(CodeAwareChunker::new()));
        chunkers.insert("sliding_window".to_string(), ChunkerType::SlidingWindow(SlidingWindowChunker::new()));
        chunkers.insert("adaptive".to_string(), ChunkerType::Adaptive(AdaptiveChunker::new()));

        Self { chunkers }
    }

    pub async fn run_comprehensive_benchmark(
        &self,
        test_contents: &[TestContent],
        configs: &[ChunkingConfig],
    ) -> Result<BenchmarkSuite> {
        let mut all_benchmarks = Vec::new();

        for test_content in test_contents {
            for config in configs {
                for (strategy_name, chunker) in &self.chunkers {
                    if chunker.supports_content_type(&test_content.content_type) {
                        let benchmark = self.benchmark_strategy(
                            strategy_name,
                            chunker.as_ref(),
                            &test_content.content,
                            &test_content.file_path,
                            &test_content.content_type,
                            config,
                        ).await?;
                        
                        all_benchmarks.push(benchmark);
                    }
                }
            }
        }

        let summary = self.generate_summary(&all_benchmarks);
        
        Ok(BenchmarkSuite {
            benchmarks: all_benchmarks,
            summary,
        })
    }

    async fn benchmark_strategy(
        &self,
        strategy_name: &str,
        chunker: &ChunkerType,
        content: &str,
        file_path: &Path,
        content_type: &ContentType,
        config: &ChunkingConfig,
    ) -> Result<ChunkingBenchmark> {
        let start_memory = self.get_memory_usage();
        let start_time = Instant::now();

        let result = chunker.chunk_document(content, file_path, config).await?;

        let processing_time = start_time.elapsed().as_millis() as u64;
        let end_memory = self.get_memory_usage();
        let memory_usage = end_memory - start_memory;

        let quality_metrics = self.calculate_quality_metrics(&result, content);
        let throughput = if processing_time > 0 {
            content.len() as f32 / processing_time as f32
        } else {
            0.0
        };

        Ok(ChunkingBenchmark {
            strategy_name: strategy_name.to_string(),
            content_type: format!("{:?}", content_type),
            content_size: content.len(),
            processing_time_ms: processing_time,
            total_chunks: result.metadata.total_chunks,
            average_chunk_size: result.metadata.average_chunk_size,
            size_variance: result.metadata.size_variance,
            overlap_ratio: result.metadata.overlap_ratio,
            boundary_preservation_score: result.metadata.boundary_preservation_score,
            memory_usage_mb: memory_usage,
            throughput_chars_per_ms: throughput,
            quality_metrics,
        })
    }

    fn calculate_quality_metrics(
        &self,
        result: &ChunkedDocument,
        original_content: &str,
    ) -> QualityMetrics {
        let semantic_coherence = self.calculate_semantic_coherence(&result.chunks);
        let context_preservation = self.calculate_context_preservation(&result.chunks, original_content);
        let boundary_accuracy = result.metadata.boundary_preservation_score;
        let size_consistency = self.calculate_size_consistency(&result.chunks);
        let overlap_efficiency = self.calculate_overlap_efficiency(&result.chunks);

        QualityMetrics {
            semantic_coherence_score: semantic_coherence,
            context_preservation_score: context_preservation,
            boundary_accuracy_score: boundary_accuracy,
            size_consistency_score: size_consistency,
            overlap_efficiency_score: overlap_efficiency,
        }
    }

    fn calculate_semantic_coherence(&self, chunks: &[super::EnhancedTextChunk]) -> f32 {
        if chunks.is_empty() {
            return 0.0;
        }

        let mut coherence_sum = 0.0;
        let mut count = 0;

        for chunk in chunks {
            let sentences = chunk.content.split(&['.', '!', '?'][..]).count();
            let words = chunk.content.split_whitespace().count();
            
            if words > 0 {
                let sentence_density = sentences as f32 / words as f32;
                let coherence_score = (sentence_density * 10.0).min(1.0);
                coherence_sum += coherence_score;
                count += 1;
            }
        }

        if count > 0 {
            coherence_sum / count as f32
        } else {
            0.0
        }
    }

    fn calculate_context_preservation(&self, chunks: &[super::EnhancedTextChunk], _original: &str) -> f32 {
        if chunks.len() < 2 {
            return 1.0;
        }

        let mut preservation_score = 0.0;
        let mut count = 0;

        for i in 1..chunks.len() {
            let prev_chunk = &chunks[i - 1];
            let curr_chunk = &chunks[i];

            if let (Some(prev_end), Some(curr_start)) = (
                prev_chunk.metadata.context_after.as_ref(),
                curr_chunk.metadata.context_before.as_ref(),
            ) {
                let overlap_words: std::collections::HashSet<&str> = prev_end
                    .split_whitespace()
                    .collect();
                let current_words: std::collections::HashSet<&str> = curr_start
                    .split_whitespace()
                    .collect();
                
                let intersection_size = overlap_words.intersection(&current_words).count();
                let union_size = overlap_words.union(&current_words).count();
                
                if union_size > 0 {
                    preservation_score += intersection_size as f32 / union_size as f32;
                    count += 1;
                }
            }
        }

        if count > 0 {
            preservation_score / count as f32
        } else {
            0.5
        }
    }

    fn calculate_size_consistency(&self, chunks: &[super::EnhancedTextChunk]) -> f32 {
        if chunks.is_empty() {
            return 0.0;
        }

        let sizes: Vec<usize> = chunks.iter().map(|c| c.metadata.token_count).collect();
        let mean = sizes.iter().sum::<usize>() as f32 / sizes.len() as f32;
        
        if mean == 0.0 {
            return 0.0;
        }

        let variance = sizes.iter()
            .map(|&size| (size as f32 - mean).powi(2))
            .sum::<f32>() / sizes.len() as f32;
        
        let coefficient_of_variation = variance.sqrt() / mean;
        
        (1.0 - coefficient_of_variation.min(1.0)).max(0.0)
    }

    fn calculate_overlap_efficiency(&self, chunks: &[super::EnhancedTextChunk]) -> f32 {
        if chunks.len() < 2 {
            return 1.0;
        }

        let mut total_overlap = 0;
        let mut total_content = 0;

        for chunk in chunks {
            total_content += chunk.metadata.char_count;
            if let Some(overlap_start) = chunk.metadata.overlap_start {
                total_overlap += overlap_start * 4;
            }
            if let Some(overlap_end) = chunk.metadata.overlap_end {
                total_overlap += overlap_end * 4;
            }
        }

        if total_content == 0 {
            return 0.0;
        }

        let overlap_ratio = total_overlap as f32 / total_content as f32;
        
        if overlap_ratio > 0.5 {
            1.0 - (overlap_ratio - 0.5) * 2.0
        } else if overlap_ratio < 0.1 {
            overlap_ratio * 10.0
        } else {
            1.0
        }
    }

    fn generate_summary(&self, benchmarks: &[ChunkingBenchmark]) -> BenchmarkSummary {
        let mut strategy_scores: HashMap<String, Vec<f32>> = HashMap::new();
        let mut content_type_best: HashMap<String, (String, f32)> = HashMap::new();

        for benchmark in benchmarks {
            let overall_score = self.calculate_overall_score(benchmark);
            
            strategy_scores
                .entry(benchmark.strategy_name.clone())
                .or_insert_with(Vec::new)
                .push(overall_score);

            let current_best = content_type_best
                .entry(benchmark.content_type.clone())
                .or_insert((benchmark.strategy_name.clone(), overall_score));
            
            if overall_score > current_best.1 {
                *current_best = (benchmark.strategy_name.clone(), overall_score);
            }
        }

        let mut rankings = Vec::new();
        for (strategy, scores) in strategy_scores {
            let avg_score = scores.iter().sum::<f32>() / scores.len() as f32;
            let performance_score = self.calculate_performance_score(&strategy, benchmarks);
            let quality_score = self.calculate_quality_score(&strategy, benchmarks);
            let consistency_score = self.calculate_consistency_score(&scores);

            rankings.push(StrategyRanking {
                strategy: strategy.clone(),
                overall_score: avg_score,
                performance_score,
                quality_score,
                consistency_score,
            });
        }

        rankings.sort_by(|a, b| b.overall_score.partial_cmp(&a.overall_score).unwrap());

        let best_overall = rankings.first()
            .map(|r| r.strategy.clone())
            .unwrap_or_else(|| "sliding_window".to_string());

        BenchmarkSummary {
            best_overall_strategy: best_overall,
            best_for_code: content_type_best
                .get("Code")
                .map(|(s, _)| s.clone())
                .unwrap_or_else(|| "code_aware".to_string()),
            best_for_natural_language: content_type_best
                .get("NaturalLanguage")
                .map(|(s, _)| s.clone())
                .unwrap_or_else(|| "semantic".to_string()),
            best_for_mixed_content: content_type_best
                .get("Mixed")
                .map(|(s, _)| s.clone())
                .unwrap_or_else(|| "adaptive".to_string()),
            performance_rankings: rankings,
        }
    }

    fn calculate_overall_score(&self, benchmark: &ChunkingBenchmark) -> f32 {
        let performance_weight = 0.3;
        let quality_weight = 0.5;
        let consistency_weight = 0.2;

        let performance_score = if benchmark.processing_time_ms > 0 {
            (1000.0 / benchmark.processing_time_ms as f32).min(1.0)
        } else {
            1.0
        };

        let quality_score = (
            benchmark.quality_metrics.semantic_coherence_score +
            benchmark.quality_metrics.context_preservation_score +
            benchmark.quality_metrics.boundary_accuracy_score +
            benchmark.quality_metrics.overlap_efficiency_score
        ) / 4.0;

        let consistency_score = benchmark.quality_metrics.size_consistency_score;

        performance_score * performance_weight +
        quality_score * quality_weight +
        consistency_score * consistency_weight
    }

    fn calculate_performance_score(&self, strategy: &str, benchmarks: &[ChunkingBenchmark]) -> f32 {
        let strategy_benchmarks: Vec<&ChunkingBenchmark> = benchmarks
            .iter()
            .filter(|b| b.strategy_name == strategy)
            .collect();

        if strategy_benchmarks.is_empty() {
            return 0.0;
        }

        let avg_throughput = strategy_benchmarks
            .iter()
            .map(|b| b.throughput_chars_per_ms)
            .sum::<f32>() / strategy_benchmarks.len() as f32;

        avg_throughput.min(1.0)
    }

    fn calculate_quality_score(&self, strategy: &str, benchmarks: &[ChunkingBenchmark]) -> f32 {
        let strategy_benchmarks: Vec<&ChunkingBenchmark> = benchmarks
            .iter()
            .filter(|b| b.strategy_name == strategy)
            .collect();

        if strategy_benchmarks.is_empty() {
            return 0.0;
        }

        let avg_quality = strategy_benchmarks
            .iter()
            .map(|b| {
                (b.quality_metrics.semantic_coherence_score +
                 b.quality_metrics.context_preservation_score +
                 b.quality_metrics.boundary_accuracy_score +
                 b.quality_metrics.overlap_efficiency_score) / 4.0
            })
            .sum::<f32>() / strategy_benchmarks.len() as f32;

        avg_quality
    }

    fn calculate_consistency_score(&self, scores: &[f32]) -> f32 {
        if scores.len() < 2 {
            return 1.0;
        }

        let mean = scores.iter().sum::<f32>() / scores.len() as f32;
        let variance = scores.iter()
            .map(|&score| (score - mean).powi(2))
            .sum::<f32>() / scores.len() as f32;
        
        let std_dev = variance.sqrt();
        (1.0 - std_dev).max(0.0)
    }

    fn get_memory_usage(&self) -> f32 {
        0.0
    }
}

#[derive(Debug, Clone)]
pub struct TestContent {
    pub content: String,
    pub file_path: std::path::PathBuf,
    pub content_type: ContentType,
    pub description: String,
}

impl TestContent {
    pub fn new(content: String, file_path: std::path::PathBuf, content_type: ContentType, description: String) -> Self {
        Self {
            content,
            file_path,
            content_type,
            description,
        }
    }
}
