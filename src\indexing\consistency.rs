use crate::error::Result;
use crate::storage::Storage;
use crate::search::SearchEngine;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IndexHealth {
    pub overall_status: HealthStatus,
    pub issues: Vec<ConsistencyIssue>,
    pub statistics: HealthStatistics,
    pub last_check: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ConsistencyIssue {
    pub issue_type: IssueType,
    pub path: PathBuf,
    pub description: String,
    pub severity: IssueSeverity,
    pub suggested_action: RepairAction,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum IssueType {
    MissingFile,
    OrphanedIndex,
    HashMismatch,
    CorruptedMetadata,
    DuplicateEntry,
    InconsistentTimestamp,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum IssueSeverity {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RepairAction {
    RemoveFromIndex,
    ReindexFile,
    UpdateMetadata,
    MergeEntries,
    FullReindex,
    ManualIntervention,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatistics {
    pub total_documents: usize,
    pub indexed_documents: usize,
    pub missing_files: usize,
    pub orphaned_entries: usize,
    pub hash_mismatches: usize,
    pub total_size_bytes: u64,
    pub index_size_bytes: u64,
    pub fragmentation_ratio: f64,
}

#[derive(Clone)]
pub struct ConsistencyChecker {
    storage: Arc<Storage>,
    search_engine: Arc<SearchEngine>,
}

impl ConsistencyChecker {
    pub async fn new(index_path: &Path) -> Result<Self> {
        let storage_config = crate::config::StorageConfig {
            compression_level: 6,
            enable_encryption: false,
            backup_enabled: false,
            backup_interval_hours: 24,
            max_backup_files: 7,
            backend: "sled".to_string(),
            path: index_path.join("storage"),
            cache_size_mb: 100,
            sync_interval_ms: 5000,
        };
        let storage = Arc::new(Storage::new(&storage_config, index_path).await?);
        let config = crate::config::Config::default();
        let search_engine = Arc::new(SearchEngine::new(&config).await?);

        Ok(Self {
            storage,
            search_engine,
        })
    }

    pub async fn check_health(&self) -> Result<IndexHealth> {
        let start_time = std::time::Instant::now();
        let mut issues = Vec::new();

        tracing::info!("Starting consistency check...");

        // Get all indexed documents
        let indexed_documents = self.storage.list_all_documents().await?;
        let search_documents = self.search_engine.list_all_documents().await?;

        // Check for missing files
        issues.extend(self.check_missing_files(&indexed_documents).await?);

        // Check for orphaned index entries
        issues.extend(self.check_orphaned_entries(&indexed_documents, &search_documents).await?);

        // Check for hash mismatches
        issues.extend(self.check_hash_mismatches(&indexed_documents).await?);

        // Check for duplicates
        issues.extend(self.check_duplicates(&indexed_documents).await?);

        // Check for timestamp inconsistencies
        issues.extend(self.check_timestamp_consistency(&indexed_documents).await?);

        // Calculate statistics
        let statistics = self.calculate_statistics(&indexed_documents, &issues).await?;

        // Determine overall health status
        let overall_status = self.determine_health_status(&issues);

        let duration = start_time.elapsed();
        tracing::info!(
            "Consistency check completed in {:?}. Found {} issues.",
            duration,
            issues.len()
        );

        Ok(IndexHealth {
            overall_status,
            issues,
            statistics,
            last_check: chrono::Utc::now(),
        })
    }

    async fn check_missing_files(
        &self,
        indexed_documents: &HashMap<PathBuf, crate::indexing::builder::DocumentMetadata>,
    ) -> Result<Vec<ConsistencyIssue>> {
        let mut issues = Vec::new();

        for (path, _metadata) in indexed_documents {
            if !path.exists() {
                issues.push(ConsistencyIssue {
                    issue_type: IssueType::MissingFile,
                    path: path.clone(),
                    description: format!("File {} no longer exists but is still indexed", path.display()),
                    severity: IssueSeverity::Medium,
                    suggested_action: RepairAction::RemoveFromIndex,
                });
            }
        }

        Ok(issues)
    }

    async fn check_orphaned_entries(
        &self,
        indexed_documents: &HashMap<PathBuf, crate::indexing::builder::DocumentMetadata>,
        search_documents: &HashSet<PathBuf>,
    ) -> Result<Vec<ConsistencyIssue>> {
        let mut issues = Vec::new();

        // Check for documents in search index but not in storage
        for search_path in search_documents {
            if !indexed_documents.contains_key(search_path) {
                issues.push(ConsistencyIssue {
                    issue_type: IssueType::OrphanedIndex,
                    path: search_path.clone(),
                    description: format!("Document {} exists in search index but not in storage", search_path.display()),
                    severity: IssueSeverity::Medium,
                    suggested_action: RepairAction::RemoveFromIndex,
                });
            }
        }

        // Check for documents in storage but not in search index
        for storage_path in indexed_documents.keys() {
            if !search_documents.contains(storage_path) {
                issues.push(ConsistencyIssue {
                    issue_type: IssueType::OrphanedIndex,
                    path: storage_path.clone(),
                    description: format!("Document {} exists in storage but not in search index", storage_path.display()),
                    severity: IssueSeverity::High,
                    suggested_action: RepairAction::ReindexFile,
                });
            }
        }

        Ok(issues)
    }

    async fn check_hash_mismatches(
        &self,
        indexed_documents: &HashMap<PathBuf, crate::indexing::builder::DocumentMetadata>,
    ) -> Result<Vec<ConsistencyIssue>> {
        let mut issues = Vec::new();

        for (path, metadata) in indexed_documents {
            if path.exists() {
                // Read current file content
                if let Ok(current_content) = fs::read_to_string(path).await {
                    let current_hash = self.compute_content_hash(&current_content);
                    
                    if current_hash != metadata.content_hash {
                        issues.push(ConsistencyIssue {
                            issue_type: IssueType::HashMismatch,
                            path: path.clone(),
                            description: format!(
                                "Content hash mismatch for {}. Expected: {}, Found: {}",
                                path.display(),
                                metadata.content_hash,
                                current_hash
                            ),
                            severity: IssueSeverity::High,
                            suggested_action: RepairAction::ReindexFile,
                        });
                    }
                }
            }
        }

        Ok(issues)
    }

    async fn check_duplicates(
        &self,
        indexed_documents: &HashMap<PathBuf, crate::indexing::builder::DocumentMetadata>,
    ) -> Result<Vec<ConsistencyIssue>> {
        let mut issues = Vec::new();
        let mut hash_to_paths: HashMap<String, Vec<PathBuf>> = HashMap::new();

        // Group documents by content hash
        for (path, metadata) in indexed_documents {
            hash_to_paths
                .entry(metadata.content_hash.clone())
                .or_default()
                .push(path.clone());
        }

        // Find duplicates
        for (hash, paths) in hash_to_paths {
            if paths.len() > 1 {
                for path in &paths[1..] { // Skip the first one
                    issues.push(ConsistencyIssue {
                        issue_type: IssueType::DuplicateEntry,
                        path: path.clone(),
                        description: format!(
                            "Duplicate content detected. File {} has same content as {}",
                            path.display(),
                            paths[0].display()
                        ),
                        severity: IssueSeverity::Low,
                        suggested_action: RepairAction::MergeEntries,
                    });
                }
            }
        }

        Ok(issues)
    }

    async fn check_timestamp_consistency(
        &self,
        indexed_documents: &HashMap<PathBuf, crate::indexing::builder::DocumentMetadata>,
    ) -> Result<Vec<ConsistencyIssue>> {
        let mut issues = Vec::new();

        for (path, metadata) in indexed_documents {
            if path.exists() {
                if let Ok(file_metadata) = fs::metadata(path).await {
                    if let Ok(modified_time) = file_metadata.modified() {
                        let file_timestamp = chrono::DateTime::from(modified_time);
                        
                        // Allow for small differences due to filesystem precision
                        let diff = (file_timestamp - metadata.modified_time).num_seconds().abs();
                        
                        if diff > 2 { // More than 2 seconds difference
                            issues.push(ConsistencyIssue {
                                issue_type: IssueType::InconsistentTimestamp,
                                path: path.clone(),
                                description: format!(
                                    "Timestamp mismatch for {}. File: {}, Indexed: {}",
                                    path.display(),
                                    file_timestamp.format("%Y-%m-%d %H:%M:%S"),
                                    metadata.modified_time.format("%Y-%m-%d %H:%M:%S")
                                ),
                                severity: IssueSeverity::Medium,
                                suggested_action: RepairAction::UpdateMetadata,
                            });
                        }
                    }
                }
            }
        }

        Ok(issues)
    }

    async fn calculate_statistics(
        &self,
        indexed_documents: &HashMap<PathBuf, crate::indexing::builder::DocumentMetadata>,
        issues: &[ConsistencyIssue],
    ) -> Result<HealthStatistics> {
        let total_documents = indexed_documents.len();
        let missing_files = issues.iter().filter(|i| matches!(i.issue_type, IssueType::MissingFile)).count();
        let orphaned_entries = issues.iter().filter(|i| matches!(i.issue_type, IssueType::OrphanedIndex)).count();
        let hash_mismatches = issues.iter().filter(|i| matches!(i.issue_type, IssueType::HashMismatch)).count();

        let total_size_bytes: u64 = indexed_documents.values().map(|m| m.file_size).sum();
        
        // Estimate index size (simplified)
        let index_size_bytes = total_size_bytes / 10; // Rough estimate
        
        let fragmentation_ratio = if total_size_bytes > 0 {
            index_size_bytes as f64 / total_size_bytes as f64
        } else {
            0.0
        };

        Ok(HealthStatistics {
            total_documents,
            indexed_documents: total_documents - missing_files,
            missing_files,
            orphaned_entries,
            hash_mismatches,
            total_size_bytes,
            index_size_bytes,
            fragmentation_ratio,
        })
    }

    fn determine_health_status(&self, issues: &[ConsistencyIssue]) -> HealthStatus {
        let critical_count = issues.iter().filter(|i| matches!(i.severity, IssueSeverity::Critical)).count();
        let high_count = issues.iter().filter(|i| matches!(i.severity, IssueSeverity::High)).count();
        let medium_count = issues.iter().filter(|i| matches!(i.severity, IssueSeverity::Medium)).count();

        if critical_count > 0 || high_count > 10 {
            HealthStatus::Critical
        } else if high_count > 0 || medium_count > 20 {
            HealthStatus::Warning
        } else {
            HealthStatus::Healthy
        }
    }

    pub async fn repair(&self, health: &IndexHealth) -> Result<()> {
        tracing::info!("Starting repair process for {} issues", health.issues.len());

        for issue in &health.issues {
            match self.repair_issue(issue).await {
                Ok(_) => {
                    tracing::debug!("Repaired issue: {}", issue.description);
                }
                Err(e) => {
                    tracing::error!("Failed to repair issue {}: {}", issue.description, e);
                }
            }
        }

        tracing::info!("Repair process completed");
        Ok(())
    }

    async fn repair_issue(&self, issue: &ConsistencyIssue) -> Result<()> {
        match issue.suggested_action {
            RepairAction::RemoveFromIndex => {
                self.storage.delete_document(&issue.path).await?;
                self.search_engine.remove_document(&issue.path).await?;
            }
            RepairAction::ReindexFile => {
                if issue.path.exists() {
                    if let Ok(content) = fs::read_to_string(&issue.path).await {
                        self.search_engine.index_document(&issue.path, &content).await?;
                    }
                }
            }
            RepairAction::UpdateMetadata => {
                // Update metadata with current file information
                if issue.path.exists() {
                    if let Ok(content) = fs::read_to_string(&issue.path).await {
                        let metadata = self.create_current_metadata(&issue.path, &content).await?;
                        self.storage.store_document_with_metadata(&issue.path, &content, &metadata).await?;
                    }
                }
            }
            RepairAction::MergeEntries => {
                // For now, just remove the duplicate
                self.storage.delete_document(&issue.path).await?;
                self.search_engine.remove_document(&issue.path).await?;
            }
            RepairAction::FullReindex => {
                // This would require a full system reindex
                tracing::warn!("Full reindex required for {}", issue.path.display());
            }
            RepairAction::ManualIntervention => {
                tracing::warn!("Manual intervention required for {}", issue.path.display());
            }
        }

        Ok(())
    }

    fn compute_content_hash(&self, content: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    async fn create_current_metadata(
        &self,
        path: &Path,
        content: &str,
    ) -> Result<crate::indexing::builder::DocumentMetadata> {
        let file_metadata = fs::metadata(path).await?;
        let modified_time = file_metadata
            .modified()?
            .duration_since(std::time::UNIX_EPOCH)?
            .as_secs();

        let file_type = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown")
            .to_string();

        let estimated_chunks = (content.len() / 512).max(1);

        Ok(crate::indexing::builder::DocumentMetadata {
            file_size: file_metadata.len(),
            modified_time: chrono::DateTime::from_timestamp(modified_time as i64, 0)
                .unwrap_or_else(chrono::Utc::now),
            content_hash: self.compute_content_hash(content),
            file_type,
            encoding: Some("utf-8".to_string()),
            chunk_count: estimated_chunks,
        })
    }
}
