pub mod vector_store;
pub mod hnsw_index;
pub mod quantization;
pub mod persistence;
pub mod query_cache;
pub mod metrics;

pub use vector_store::{VectorStore, VectorStoreConfig, VectorStoreStats, SearchResult, SearchQuery, HnswVectorStore};
pub use hnsw_index::{HnswIndex, HnswConfig, IndexStats};
pub use quantization::{VectorQuantizer, QuantizationConfig, QuantizationType};
pub use persistence::{IndexPersistence, PersistenceConfig};
pub use query_cache::{QueryCache, CacheConfig};
pub use metrics::{SearchMetrics, PerformanceStats, PerformanceMonitor, AlertThresholds};

use serde::{Deserialize, Serialize};
use std::collections::HashMap;


pub type VectorId = u64;


pub type Vector = Vec<f32>;


#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DistanceMetric {
    
    Euclidean,
    
    <PERSON><PERSON><PERSON>,
    
    Manhattan,
    
    DotProduct,
}


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchConfig {
    
    pub k: usize,
    
    pub max_distance: Option<f32>,
    
    pub accuracy: f32,
    
    pub enable_cache: bool,
    
    pub filters: HashMap<String, String>,
}

impl Default for SearchConfig {
    fn default() -> Self {
        Self {
            k: 10,
            max_distance: None,
            accuracy: 0.95,
            enable_cache: true,
            filters: HashMap::new(),
        }
    }
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorMetadata {
    
    pub labels: HashMap<String, String>,
    
    pub created_at: chrono::DateTime<chrono::Utc>,
    
    pub payload: Option<serde_json::Value>,
}

impl Default for VectorMetadata {
    fn default() -> Self {
        Self {
            labels: HashMap::new(),
            created_at: chrono::Utc::now(),
            payload: None,
        }
    }
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorEntry {
    pub id: VectorId,
    pub vector: Vector,
    pub metadata: VectorMetadata,
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimilarityResult {
    pub id: VectorId,
    pub distance: f32,
    pub similarity: f32,
    pub metadata: VectorMetadata,
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchResult {
    pub successful: Vec<VectorId>,
    pub failed: Vec<(VectorId, String)>,
    pub total_time_ms: u64,
}


pub type ProgressCallback = Box<dyn Fn(usize, usize) + Send + Sync>;


pub mod similarity {
    use super::*;
    use rayon::prelude::*;
    
    
    #[inline]
    pub fn euclidean_distance(a: &[f32], b: &[f32]) -> f32 {
        debug_assert_eq!(a.len(), b.len());
        a.iter()
            .zip(b.iter())
            .map(|(x, y)| (x - y).powi(2))
            .sum::<f32>()
            .sqrt()
    }
    
    
    #[inline]
    pub fn cosine_distance(a: &[f32], b: &[f32]) -> f32 {
        debug_assert_eq!(a.len(), b.len());
        
        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();
        
        if norm_a == 0.0 || norm_b == 0.0 {
            return 1.0; 
        }
        
        1.0 - (dot_product / (norm_a * norm_b))
    }
    
    
    #[inline]
    pub fn manhattan_distance(a: &[f32], b: &[f32]) -> f32 {
        debug_assert_eq!(a.len(), b.len());
        a.iter().zip(b.iter()).map(|(x, y)| (x - y).abs()).sum()
    }
    
    
    #[inline]
    pub fn dot_product_distance(a: &[f32], b: &[f32]) -> f32 {
        debug_assert_eq!(a.len(), b.len());
        -a.iter().zip(b.iter()).map(|(x, y)| x * y).sum::<f32>()
    }
    
    
    pub fn calculate_distance(a: &[f32], b: &[f32], metric: DistanceMetric) -> f32 {
        match metric {
            DistanceMetric::Euclidean => euclidean_distance(a, b),
            DistanceMetric::Cosine => cosine_distance(a, b),
            DistanceMetric::Manhattan => manhattan_distance(a, b),
            DistanceMetric::DotProduct => dot_product_distance(a, b),
        }
    }
    
    
    pub fn distance_to_similarity(distance: f32, metric: DistanceMetric) -> f32 {
        match metric {
            DistanceMetric::Euclidean | DistanceMetric::Manhattan => {
                1.0 / (1.0 + distance)
            }
            DistanceMetric::Cosine => {
                1.0 - distance
            }
            DistanceMetric::DotProduct => {
                
                -distance
            }
        }
    }
    
    
    pub fn batch_distances(
        query: &[f32],
        vectors: &[Vec<f32>],
        metric: DistanceMetric,
    ) -> Vec<f32> {
        vectors
            .par_iter()
            .map(|v| calculate_distance(query, v, metric))
            .collect()
    }
}


pub mod utils {
    use super::*;
    
    
    pub fn normalize_vector(vector: &mut [f32]) {
        let norm: f32 = vector.iter().map(|x| x * x).sum::<f32>().sqrt();
        if norm > 0.0 {
            for x in vector.iter_mut() {
                *x /= norm;
            }
        }
    }
    
    
    pub fn random_vector(dim: usize) -> Vector {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        (0..dim).map(|_| rng.gen_range(-1.0..1.0)).collect()
    }
    
    
    pub fn vector_stats(vectors: &[Vector]) -> (f32, f32, f32) {
        if vectors.is_empty() {
            return (0.0, 0.0, 0.0);
        }
        
        let total_elements = vectors.len() * vectors[0].len();
        let sum: f32 = vectors.iter().flatten().sum();
        let mean = sum / total_elements as f32;
        
        let variance: f32 = vectors
            .iter()
            .flatten()
            .map(|x| (x - mean).powi(2))
            .sum::<f32>() / total_elements as f32;
        
        let std_dev = variance.sqrt();
        
        (mean, std_dev, variance)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_euclidean_distance() {
        let a = vec![1.0, 2.0, 3.0];
        let b = vec![4.0, 5.0, 6.0];
        let distance = similarity::euclidean_distance(&a, &b);
        assert!((distance - 5.196152).abs() < 1e-5);
    }
    
    #[test]
    fn test_cosine_distance() {
        let a = vec![1.0, 0.0, 0.0];
        let b = vec![0.0, 1.0, 0.0];
        let distance = similarity::cosine_distance(&a, &b);
        assert!((distance - 1.0).abs() < 1e-5); 
    }
    
    #[test]
    fn test_vector_normalization() {
        let mut vector = vec![3.0, 4.0];
        utils::normalize_vector(&mut vector);
        let norm: f32 = vector.iter().map(|x| x * x).sum::<f32>().sqrt();
        assert!((norm - 1.0).abs() < 1e-5);
    }
}
