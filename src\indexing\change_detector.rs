use crate::error::Result;
use crate::indexing::builder::DocumentMetadata;
use crate::indexing::watcher::{WatchEvent, WatchEventKind};
use crate::storage::Storage;
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FileChange {
    pub path: PathBuf,
    pub change_type: ChangeType,
    pub content: Option<String>,
    pub metadata: Option<DocumentMetadata>,
    pub previous_hash: Option<String>,
    pub new_hash: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChangeType {
    Created,
    Modified,
    Deleted,
    Moved { from: PathBuf, to: PathBuf },
}

pub struct ChangeDetector {
    storage: Arc<Storage>,
    file_hashes: Arc<DashMap<PathBuf, String>>,
    pending_moves: Arc<DashMap<String, (PathBuf, std::time::Instant)>>,
    move_timeout_ms: u64,
}

impl ChangeDetector {
    pub async fn new(index_path: &Path) -> Result<Self> {
        let storage = Arc::new(Storage::new(index_path).await?);
        let file_hashes = Arc::new(DashMap::new());

        // Load existing file hashes
        let detector = Self {
            storage: storage.clone(),
            file_hashes: file_hashes.clone(),
            pending_moves: Arc::new(DashMap::new()),
            move_timeout_ms: 5000, // 5 seconds to detect moves
        };

        detector.load_existing_hashes().await?;
        Ok(detector)
    }

    async fn load_existing_hashes(&self) -> Result<()> {
        let documents = self.storage.list_all_documents().await?;
        
        for (path, metadata) in documents {
            self.file_hashes.insert(path, metadata.content_hash);
        }

        tracing::info!("Loaded {} existing file hashes", self.file_hashes.len());
        Ok(())
    }

    pub async fn process_events(&self, events: Vec<WatchEvent>) -> Result<Vec<FileChange>> {
        let mut changes = Vec::new();
        let mut processed_paths = HashSet::new();

        // Group events by path to handle duplicates
        let mut path_events: HashMap<PathBuf, Vec<WatchEvent>> = HashMap::new();
        for event in events {
            path_events.entry(event.path.clone()).or_default().push(event);
        }

        // Process each path's events
        for (path, mut path_events) in path_events {
            if processed_paths.contains(&path) {
                continue;
            }

            // Sort events by timestamp
            path_events.sort_by_key(|e| e.timestamp);

            // Take the most recent event for this path
            if let Some(latest_event) = path_events.last() {
                if let Some(change) = self.process_single_event(latest_event.clone()).await? {
                    changes.push(change);
                    processed_paths.insert(path);
                }
            }
        }

        // Clean up old pending moves
        self.cleanup_pending_moves().await;

        Ok(changes)
    }

    async fn process_single_event(&self, event: WatchEvent) -> Result<Option<FileChange>> {
        match event.kind {
            WatchEventKind::Created => {
                self.handle_created_event(event.path).await
            }
            WatchEventKind::Modified => {
                self.handle_modified_event(event.path).await
            }
            WatchEventKind::Deleted => {
                self.handle_deleted_event(event.path).await
            }
            WatchEventKind::Renamed { from, to } => {
                self.handle_renamed_event(from, to).await
            }
            WatchEventKind::Other(_) => {
                // Treat as potential modification
                self.handle_modified_event(event.path).await
            }
        }
    }

    async fn handle_created_event(&self, path: PathBuf) -> Result<Option<FileChange>> {
        // Check if file actually exists and is readable
        if !path.exists() || !path.is_file() {
            return Ok(None);
        }

        // Read file content and compute hash
        let content = match fs::read_to_string(&path).await {
            Ok(content) => content,
            Err(_) => {
                // File might be binary or unreadable
                return Ok(None);
            }
        };

        let new_hash = self.compute_content_hash(&content);
        let metadata = self.create_metadata(&path, &content).await?;

        // Check if this might be a move operation
        if let Some(original_path) = self.check_for_move(&new_hash).await {
            // This is likely a move operation
            self.file_hashes.remove(&original_path);
            self.file_hashes.insert(path.clone(), new_hash.clone());

            return Ok(Some(FileChange {
                path: path.clone(),
                change_type: ChangeType::Moved {
                    from: original_path,
                    to: path,
                },
                content: Some(content),
                metadata: Some(metadata),
                previous_hash: Some(new_hash.clone()),
                new_hash: Some(new_hash),
            }));
        }

        // Store new hash
        self.file_hashes.insert(path.clone(), new_hash.clone());

        Ok(Some(FileChange {
            path,
            change_type: ChangeType::Created,
            content: Some(content),
            metadata: Some(metadata),
            previous_hash: None,
            new_hash: Some(new_hash),
        }))
    }

    async fn handle_modified_event(&self, path: PathBuf) -> Result<Option<FileChange>> {
        // Check if file exists
        if !path.exists() || !path.is_file() {
            // File was deleted
            return self.handle_deleted_event(path).await;
        }

        // Read current content
        let content = match fs::read_to_string(&path).await {
            Ok(content) => content,
            Err(_) => {
                // File might be binary or unreadable
                return Ok(None);
            }
        };

        let new_hash = self.compute_content_hash(&content);
        let previous_hash = self.file_hashes.get(&path).map(|h| h.clone());

        // Check if content actually changed
        if let Some(ref prev_hash) = previous_hash {
            if prev_hash == &new_hash {
                // No actual change
                return Ok(None);
            }
        }

        let metadata = self.create_metadata(&path, &content).await?;

        // Update hash
        self.file_hashes.insert(path.clone(), new_hash.clone());

        let change_type = if previous_hash.is_some() {
            ChangeType::Modified
        } else {
            ChangeType::Created
        };

        Ok(Some(FileChange {
            path,
            change_type,
            content: Some(content),
            metadata: Some(metadata),
            previous_hash,
            new_hash: Some(new_hash),
        }))
    }

    async fn handle_deleted_event(&self, path: PathBuf) -> Result<Option<FileChange>> {
        let previous_hash = self.file_hashes.remove(&path).map(|(_, hash)| hash);

        if previous_hash.is_some() {
            // Add to pending moves in case this is part of a move operation
            let now = std::time::Instant::now();
            if let Some(hash) = &previous_hash {
                self.pending_moves.insert(hash.clone(), (path.clone(), now));
            }

            Ok(Some(FileChange {
                path,
                change_type: ChangeType::Deleted,
                content: None,
                metadata: None,
                previous_hash,
                new_hash: None,
            }))
        } else {
            // File wasn't tracked
            Ok(None)
        }
    }

    async fn handle_renamed_event(&self, from: PathBuf, to: PathBuf) -> Result<Option<FileChange>> {
        // Get hash from old location
        let hash = self.file_hashes.remove(&from).map(|(_, hash)| hash);

        if let Some(hash) = hash {
            // Read content from new location
            let content = match fs::read_to_string(&to).await {
                Ok(content) => content,
                Err(_) => return Ok(None),
            };

            let metadata = self.create_metadata(&to, &content).await?;

            // Update hash mapping
            self.file_hashes.insert(to.clone(), hash.clone());

            Ok(Some(FileChange {
                path: to.clone(),
                change_type: ChangeType::Moved { from, to },
                content: Some(content),
                metadata: Some(metadata),
                previous_hash: Some(hash.clone()),
                new_hash: Some(hash),
            }))
        } else {
            // Treat as new file creation
            self.handle_created_event(to).await
        }
    }

    async fn check_for_move(&self, content_hash: &str) -> Option<PathBuf> {
        // Check if we have a pending move with this hash
        if let Some((_, (original_path, _))) = self.pending_moves.remove(content_hash) {
            return Some(original_path);
        }

        None
    }

    async fn cleanup_pending_moves(&self) {
        let now = std::time::Instant::now();
        let timeout = std::time::Duration::from_millis(self.move_timeout_ms);

        self.pending_moves.retain(|_, (_, timestamp)| {
            now.duration_since(*timestamp) < timeout
        });
    }

    fn compute_content_hash(&self, content: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    async fn create_metadata(&self, path: &Path, content: &str) -> Result<DocumentMetadata> {
        let file_metadata = fs::metadata(path).await?;
        let modified_time = file_metadata
            .modified()?
            .duration_since(std::time::UNIX_EPOCH)?
            .as_secs();

        let file_type = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown")
            .to_string();

        // Estimate chunk count (simplified)
        let estimated_chunks = (content.len() / 512).max(1);

        Ok(DocumentMetadata {
            file_size: file_metadata.len(),
            modified_time: chrono::DateTime::from_timestamp(modified_time as i64, 0)
                .unwrap_or_else(chrono::Utc::now),
            content_hash: self.compute_content_hash(content),
            file_type,
            encoding: Some("utf-8".to_string()),
            chunk_count: estimated_chunks,
        })
    }

    pub async fn get_file_hash(&self, path: &Path) -> Option<String> {
        self.file_hashes.get(path).map(|h| h.clone())
    }

    pub async fn update_file_hash(&self, path: PathBuf, hash: String) {
        self.file_hashes.insert(path, hash);
    }

    pub async fn remove_file_hash(&self, path: &Path) -> Option<String> {
        self.file_hashes.remove(path).map(|(_, hash)| hash)
    }

    pub fn get_tracked_files_count(&self) -> usize {
        self.file_hashes.len()
    }
}
