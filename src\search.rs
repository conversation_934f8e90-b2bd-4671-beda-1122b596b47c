use crate::{
    config::Config,
    error::Result,
    storage::{Storage, SearchCandidate},
    vector_search::{
        VectorStore, VectorStoreConfig, VectorStoreStats, HnswVectorStore, SearchQuery, SearchConfig as VectorSearchConfig,
        VectorEntry, VectorMetadata, DistanceMetric, HnswConfig, CacheConfig,
        VectorId, similarity,
    },
};
#[cfg(feature = "embeddings")]
use crate::embeddings::{
    EmbeddingEngine, EmbeddingConfig, ModelCache, ModelCacheConfig,
    SentenceTransformerEngine, BatchProcessor, BatchProcessorConfig,
    Embedding, EmbeddingBatch
};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, debug, warn};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchResult {
    pub path: PathBuf,
    pub score: f32,
    pub snippet: String,
    pub chunk_index: usize,
    pub file_type: String,
    pub modified: Option<chrono::DateTime<chrono::Utc>>,
    pub similarity_score: Option<f32>,
    pub keyword_score: Option<f32>,
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct SearchStats {
    pub total_documents: usize,
    pub search_time_ms: u64,
    pub embedding_time_ms: u64,
    pub retrieval_time_ms: u64,
    pub ranking_time_ms: u64,
}

pub struct SearchEngine {
    config: Config,
    storage: Storage,
    #[cfg(feature = "embeddings")]
    model_cache: Arc<ModelCache>,
    #[cfg(feature = "embeddings")]
    batch_processor: Option<Arc<BatchProcessor>>,
    #[cfg(feature = "embeddings")]
    vector_store: Arc<tokio::sync::RwLock<HnswVectorStore>>,
    #[cfg(not(feature = "embeddings"))]
    _phantom: std::marker::PhantomData<()>,
}

impl SearchEngine {
    pub async fn new(config: &Config) -> Result<Self> {
        let storage = Storage::new(&config.storage, &config.index.path).await?;

        #[cfg(feature = "embeddings")]
        {
            info!("Initializing ML embeddings system...");

            // Create model cache with optimized settings
            let cache_config = ModelCacheConfig {
                max_models: 3,
                max_memory_mb: 4096,
                ttl_seconds: 3600,
                cache_dir: Some(PathBuf::from("./models")),
                preload_popular: true,
            };
            let model_cache = Arc::new(ModelCache::new(cache_config));

            // Pre-load the default model for faster first search
            let embedding_config = EmbeddingConfig {
                model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
                max_length: 512,
                normalize: true,
                batch_size: 32,
                device: "cpu".to_string(),
                cache_dir: None,
                model_params: HashMap::new(),
                use_quantization: false,
            };

            info!("Pre-loading embedding model: {}", embedding_config.model_id);
            let _engine = model_cache.get_model(embedding_config.clone()).await?;
            info!("Embedding model pre-loaded successfully");

            // Initialize batch processor for bulk operations
            let batch_config = BatchProcessorConfig {
                max_concurrent_batches: 4,
                optimal_batch_size: 64,
                max_wait_time_ms: 1000,
                adaptive_batching: true,
                memory_limit_mb: 2048,
            };

            let batch_processor = if let Ok(engine) = model_cache.get_model(embedding_config).await {
                Some(Arc::new(BatchProcessor::new(engine, batch_config)))
            } else {
                warn!("Failed to initialize batch processor");
                None
            };

            // Initialize high-performance vector store
            info!("🚀 Initializing production vector search system...");
            let vector_store_config = VectorStoreConfig {
                dimension: 384, // all-MiniLM-L6-v2 dimension
                distance_metric: DistanceMetric::Cosine,
                enable_mmap: true,
                persistence_path: Some(config.index.path.join("vector_index")),
                enable_quantization: config.search.enable_quantization.unwrap_or(false),
                hnsw_config: HnswConfig {
                    m: config.search.hnsw_m.unwrap_or(32),
                    ef_construction: config.search.hnsw_ef_construction.unwrap_or(400),
                    ef_search: config.search.hnsw_ef_search.unwrap_or(100),
                    parallel_construction: true,
                    ..Default::default()
                },
                cache_config: CacheConfig {
                    max_entries: config.search.cache_size.unwrap_or(10000),
                    enable_approximate_matching: true,
                    similarity_threshold: 0.98,
                    ..Default::default()
                },
                ..Default::default()
            };

            let vector_store = HnswVectorStore::new(vector_store_config)?;
            let vector_store = Arc::new(tokio::sync::RwLock::new(vector_store));

            info!("✅ Production vector search system initialized");

            Ok(Self {
                config: config.clone(),
                storage,
                model_cache,
                batch_processor,
                vector_store,
            })
        }

        #[cfg(not(feature = "embeddings"))]
        {
            warn!("ML embeddings not enabled. Compile with --features embeddings for semantic search.");
            Ok(Self {
                config: config.clone(),
                storage,
                _phantom: std::marker::PhantomData,
            })
        }
    }

    pub async fn search(
        &self,
        query: &str,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<(Vec<SearchResult>, SearchStats)> {
        let start_time = std::time::Instant::now();

        if query.trim().is_empty() {
            return Err(anyhow::anyhow!("Empty query"));
        }

        info!("🔍 Searching for: '{}' (limit: {}, filter: {:?})", query, limit, filter);

        #[cfg(feature = "embeddings")]
        {
            let embedding_start = std::time::Instant::now();

            // Get embedding model from cache (fast!)
            let embedding_config = EmbeddingConfig {
                model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
                max_length: 512,
                normalize: true,
                batch_size: 32,
                device: "cpu".to_string(),
                cache_dir: None,
                model_params: HashMap::new(),
                use_quantization: false,
            };

            let engine = self.model_cache.get_model(embedding_config).await?;
            let query_embedding = engine.embed_text(query).await?;
            let embedding_time = embedding_start.elapsed();

            debug!("🧠 Generated query embedding in {:.2}ms", embedding_time.as_millis());

            let retrieval_start = std::time::Instant::now();
            let mut results = if self.config.search.enable_hybrid_search {
                self.hybrid_search_optimized(query, &query_embedding, limit, filter).await?
            } else {
                self.semantic_search_optimized(&query_embedding, limit, filter).await?
            };
            let retrieval_time = retrieval_start.elapsed();

            let ranking_start = std::time::Instant::now();
            results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
            results.truncate(limit);
            let ranking_time = ranking_start.elapsed();

            let total_time = start_time.elapsed();

            let stats = SearchStats {
                total_documents: results.len(),
                search_time_ms: total_time.as_millis() as u64,
                embedding_time_ms: embedding_time.as_millis() as u64,
                retrieval_time_ms: retrieval_time.as_millis() as u64,
                ranking_time_ms: ranking_time.as_millis() as u64,
            };

            info!("✅ Search completed: {} results in {:.2}ms (embedding: {:.2}ms, retrieval: {:.2}ms, ranking: {:.2}ms)",
                  results.len(), total_time.as_millis(), embedding_time.as_millis(),
                  retrieval_time.as_millis(), ranking_time.as_millis());

            Ok((results, stats))
        }

        #[cfg(not(feature = "embeddings"))]
        {
            // Fallback to keyword search only
            warn!("🔍 Using keyword-only search (ML embeddings not enabled)");
            let results = self.keyword_search_fallback(query, limit, filter).await?;
            let total_time = start_time.elapsed();

            let stats = SearchStats {
                total_documents: results.len(),
                search_time_ms: total_time.as_millis() as u64,
                embedding_time_ms: 0,
                retrieval_time_ms: total_time.as_millis() as u64,
                ranking_time_ms: 0,
            };

            Ok((results, stats))
        }
    }

    #[cfg(feature = "embeddings")]
    async fn semantic_search_optimized(
        &self,
        query_embedding: &Embedding,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        // Use production-ready HNSW vector store for ultra-fast search
        let vector_store = self.vector_store.read().await;

        let search_query = SearchQuery {
            vector: query_embedding.vector.clone(),
            config: VectorSearchConfig {
                k: limit * 3, // Get more candidates for better ranking
                max_distance: Some(1.0 - self.config.search.similarity_threshold.unwrap_or(0.7)),
                accuracy: self.config.search.search_accuracy.unwrap_or(0.95),
                enable_cache: true,
                filters: HashMap::new(),
            },
        };

        let vector_results = vector_store.search(&search_query).await?;
        debug!("🚀 HNSW search found {} candidates in sub-ms time", vector_results.len());

        // Convert vector search results to our SearchResult format
        let mut results = Vec::with_capacity(vector_results.len());

        for vector_result in vector_results {
            // Get document content from storage using vector metadata
            if let Some(doc_path) = vector_result.metadata.labels.get("path") {
                let path = PathBuf::from(doc_path);

                if let Some(filter) = filter {
                    if !self.matches_filter(&path, filter) {
                        continue;
                    }
                }

                // Get content from vector metadata or storage
                let content = vector_result.metadata.labels.get("content")
                    .cloned()
                    .unwrap_or_else(|| "Content not available".to_string());

                let snippet = self.generate_snippet_optimized(&content, &path, query_embedding).await?;

                results.push(SearchResult {
                    path,
                    score: vector_result.similarity,
                    snippet,
                    chunk_index: vector_result.metadata.labels.get("chunk_index")
                        .and_then(|s| s.parse().ok())
                        .unwrap_or(0),
                    file_type: vector_result.metadata.labels.get("file_type")
                        .cloned()
                        .unwrap_or_else(|| "unknown".to_string()),
                    modified: vector_result.metadata.payload
                        .as_ref()
                        .and_then(|p| p.get("modified"))
                        .and_then(|v| v.as_str())
                        .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
                        .map(|dt| dt.with_timezone(&chrono::Utc)),
                    similarity_score: Some(vector_result.similarity),
                    keyword_score: None,
                });
            }
        }

        // Fallback to storage-based search if vector store is empty
        if results.is_empty() {
            debug!("🔄 Vector store empty, falling back to storage-based search");
            let similarity_threshold = self.config.search.similarity_threshold.unwrap_or(0.7);
            let candidates = self.storage.find_similar_vectors(
                &query_embedding.vector,
                limit * 3,
                similarity_threshold,
            ).await?;

            for candidate in candidates {
                if let Some(filter) = filter {
                    if !self.matches_filter(&candidate.path, filter) {
                        continue;
                    }
                }

                let snippet = self.generate_snippet_optimized(&candidate.content, &candidate.path, query_embedding).await?;

                results.push(SearchResult {
                    path: candidate.path,
                    score: candidate.score,
                    snippet,
                    chunk_index: candidate.chunk_index,
                    file_type: candidate.file_type,
                    modified: candidate.modified,
                    similarity_score: Some(candidate.score),
                    keyword_score: None,
                });
            }
        }

        debug!("🔍 Found {} semantic candidates", results.len());
        Ok(results)
    }

    #[cfg(feature = "embeddings")]
    async fn hybrid_search_optimized(
        &self,
        query: &str,
        query_embedding: &Embedding,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        // Run semantic and keyword searches in parallel for better performance
        let semantic_future = self.semantic_search_optimized(query_embedding, limit, filter);
        let keyword_future = self.keyword_search_optimized(query, limit, filter);

        let (semantic_results, keyword_results) = tokio::try_join!(semantic_future, keyword_future)?;

        debug!("🔍 Semantic: {} results, Keyword: {} results",
               semantic_results.len(), keyword_results.len());

        // Combine results with weighted scoring
        let mut combined_results = HashMap::new();
        let semantic_weight = self.config.search.semantic_weight.unwrap_or(0.7);
        let keyword_weight = self.config.search.keyword_weight.unwrap_or(0.3);

        // Add semantic results
        for result in semantic_results {
            let key = (result.path.clone(), result.chunk_index);
            let weighted_score = result.score * semantic_weight;
            combined_results.insert(key, (result, weighted_score));
        }

        // Add keyword results and combine scores
        for result in keyword_results {
            let key = (result.path.clone(), result.chunk_index);
            let weighted_score = result.score * keyword_weight;

            match combined_results.get_mut(&key) {
                Some((existing_result, existing_score)) => {
                    *existing_score += weighted_score;
                    existing_result.score = *existing_score;
                    existing_result.keyword_score = Some(result.score);
                }
                None => {
                    let mut new_result = result;
                    new_result.score = weighted_score;
                    new_result.keyword_score = Some(new_result.score / keyword_weight);
                    combined_results.insert(key, (new_result, weighted_score));
                }
            }
        }

        let results: Vec<SearchResult> = combined_results
            .into_values()
            .map(|(mut result, score)| {
                result.score = score;
                result
            })
            .collect();

        debug!("🔍 Combined {} hybrid results", results.len());
        Ok(results)
    }

    async fn hybrid_search(
        &self,
        query: &str,
        query_embedding: &[f32],
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let semantic_results = self.storage.find_similar_vectors(query_embedding, limit, 0.5).await?;
        let keyword_results = self.keyword_search(query, limit, filter).await?;

        let mut combined_results = std::collections::HashMap::new();

        for result in semantic_results {
            let key = (result.path.clone(), result.chunk_index);
            let weighted_score = result.score * self.config.search.semantic_weight.unwrap_or(0.7);
            combined_results.insert(key, (result, weighted_score));
        }

        for result in keyword_results {
            let key = (result.path.clone(), result.chunk_index);
            let weighted_score = result.score * self.config.search.keyword_weight.unwrap_or(0.3);
            
            match combined_results.get_mut(&key) {
                Some((existing_result, existing_score)) => {
                    *existing_score += weighted_score;
                    existing_result.score = *existing_score;
                }
                None => {
                    let candidate = SearchCandidate {
                        path: result.path.clone(),
                        content: result.snippet.clone(),
                        score: weighted_score,
                        chunk_index: result.chunk_index,
                        file_type: result.file_type.clone(),
                        modified: result.modified,
                    };
                    combined_results.insert(key, (candidate, weighted_score));
                }
            }
        }

        let mut results: Vec<SearchResult> = Vec::new();
        for (candidate, score) in combined_results.into_values() {
            let snippet = self.generate_snippet_basic(&candidate.content, &candidate.path).await?;
            results.push(SearchResult {
                path: candidate.path,
                score,
                snippet,
                chunk_index: candidate.chunk_index,
                file_type: candidate.file_type,
                modified: candidate.modified,
                keyword_score: Some(0.0),
                similarity_score: Some(score),
            });
        }

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(limit);

        Ok(results)
    }

    async fn keyword_search(
        &self,
        query: &str,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let keywords: Vec<&str> = query.split_whitespace().collect();
        let candidates = self.storage.find_by_keywords(&keywords, limit * 2).await?;

        let mut results = Vec::new();
        
        for candidate in candidates {
            if let Some(filter) = filter {
                if !self.matches_filter(&candidate.path, filter) {
                    continue;
                }
            }

            let snippet = self.generate_snippet_basic(&candidate.content, &candidate.path).await?;
            
            results.push(SearchResult {
                path: candidate.path,
                score: candidate.score,
                snippet,
                chunk_index: candidate.chunk_index,
                file_type: candidate.file_type,
                modified: candidate.modified,
                keyword_score: Some(0.0),
                similarity_score: Some(candidate.score),
            });
        }

        Ok(results)
    }

    #[cfg(feature = "embeddings")]
    async fn generate_snippet_optimized(&self, content: &str, _path: &PathBuf, _query_embedding: &Embedding) -> Result<String> {
        let snippet_length = self.config.search.snippet_length.unwrap_or(200);

        if content.len() <= snippet_length {
            Ok(content.to_string())
        } else {
            // TODO: Use query embedding to find most relevant part of content
            // For now, just take the beginning
            Ok(format!("{}...", &content[..snippet_length]))
        }
    }

    async fn keyword_search_optimized(
        &self,
        query: &str,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let keywords: Vec<&str> = query.split_whitespace().collect();
        let candidates = self.storage.find_by_keywords(&keywords, limit * 2).await?;

        debug!("🔍 Found {} keyword candidates", candidates.len());

        let mut results = Vec::with_capacity(candidates.len());

        for candidate in candidates {
            if let Some(filter) = filter {
                if !self.matches_filter(&candidate.path, filter) {
                    continue;
                }
            }

            let snippet = self.generate_snippet_basic(&candidate.content, &candidate.path).await?;

            results.push(SearchResult {
                path: candidate.path,
                score: candidate.score,
                snippet,
                chunk_index: candidate.chunk_index,
                file_type: candidate.file_type,
                modified: candidate.modified,
                similarity_score: None,
                keyword_score: Some(candidate.score),
            });
        }

        Ok(results)
    }

    #[cfg(not(feature = "embeddings"))]
    async fn keyword_search_fallback(
        &self,
        query: &str,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let keywords: Vec<&str> = query.split_whitespace().collect();
        let candidates = self.storage.find_by_keywords(&keywords, limit * 2).await?;

        let mut results = Vec::new();

        for candidate in candidates {
            if let Some(filter) = filter {
                if !self.matches_filter(&candidate.path, filter) {
                    continue;
                }
            }

            let snippet = self.generate_snippet_basic(&candidate.content, &candidate.path).await?;

            results.push(SearchResult {
                path: candidate.path,
                score: candidate.score,
                snippet,
                chunk_index: candidate.chunk_index,
                file_type: candidate.file_type,
                modified: candidate.modified,
                similarity_score: None,
                keyword_score: Some(candidate.score),
            });
        }

        Ok(results)
    }

    async fn generate_snippet_basic(&self, content: &str, _path: &PathBuf) -> Result<String> {
        let snippet_length = self.config.search.snippet_length.unwrap_or(200);

        if content.len() <= snippet_length {
            Ok(content.to_string())
        } else {
            Ok(format!("{}...", &content[..snippet_length]))
        }
    }

    fn matches_filter(&self, path: &PathBuf, filter: &str) -> bool {
        if filter.starts_with("ext:") {
            let ext = &filter[4..];
            return path.extension()
                .and_then(|e| e.to_str())
                .map_or(false, |e| e.eq_ignore_ascii_case(ext));
        }

        if filter.starts_with("path:") {
            let pattern = &filter[5..];
            return path.to_string_lossy().contains(pattern);
        }

        path.to_string_lossy().to_lowercase().contains(&filter.to_lowercase())
    }

    /// Bulk index documents with ML embeddings for better performance
    #[cfg(feature = "embeddings")]
    pub async fn bulk_index_documents(&self, documents: Vec<(PathBuf, String)>) -> Result<usize> {
        if documents.is_empty() {
            return Ok(0);
        }

        info!("🚀 Starting bulk indexing of {} documents with ML embeddings", documents.len());
        let start_time = std::time::Instant::now();

        // Extract texts for batch processing
        let texts: Vec<String> = documents.iter().map(|(_, content)| content.clone()).collect();

        // Use batch processor for efficient embedding generation
        if let Some(batch_processor) = &self.batch_processor {
            info!("🧠 Generating embeddings for {} documents...", texts.len());
            let embeddings = batch_processor.process_texts_streaming(
                texts,
                Box::new(|processed, total| {
                    if processed % 50 == 0 || processed == total {
                        info!("   📊 Embedding progress: {}/{} documents", processed, total);
                    }
                })
            ).await?;

            info!("✅ Generated {} embeddings in {:.2}s", embeddings.len(), start_time.elapsed().as_secs_f32());

            // Store documents with embeddings in both storage and vector store
            let mut indexed_count = 0;
            let mut vector_entries = Vec::new();

            for ((path, content), embedding) in documents.iter().zip(embeddings.into_iter()) {
                // Store in traditional storage
                if let Err(e) = self.storage.store_document_with_embedding(&path, &content, &embedding.vector).await {
                    warn!("Failed to store document {}: {}", path.display(), e);
                    continue;
                }

                // Prepare for vector store
                let mut metadata = VectorMetadata::default();
                metadata.labels.insert("path".to_string(), path.to_string_lossy().to_string());
                metadata.labels.insert("content".to_string(), content.clone());
                metadata.labels.insert("chunk_index".to_string(), "0".to_string());
                metadata.labels.insert("file_type".to_string(),
                    path.extension()
                        .and_then(|ext| ext.to_str())
                        .unwrap_or("unknown")
                        .to_string());

                // Add to vector store batch
                vector_entries.push(VectorEntry {
                    id: 0, // Will be auto-assigned
                    vector: embedding.vector,
                    metadata,
                });

                indexed_count += 1;
            }

            // Bulk add to vector store for optimal performance
            if !vector_entries.is_empty() {
                info!("🚀 Adding {} vectors to HNSW index...", vector_entries.len());
                let mut vector_store = self.vector_store.write().await;
                let batch_result = vector_store.add_vectors(vector_entries).await?;
                info!("✅ Added {} vectors to HNSW index in {:.2}ms",
                      batch_result.successful.len(), batch_result.total_time_ms);
            }

            let total_time = start_time.elapsed();
            info!("🎉 Bulk indexing completed: {}/{} documents in {:.2}s ({:.1} docs/sec)",
                  indexed_count, documents.len(), total_time.as_secs_f32(),
                  indexed_count as f32 / total_time.as_secs_f32());

            Ok(indexed_count)
        } else {
            warn!("Batch processor not available, falling back to individual indexing");
            self.bulk_index_documents_fallback(documents).await
        }
    }

    /// Fallback bulk indexing without ML embeddings
    pub async fn bulk_index_documents_fallback(&self, documents: Vec<(PathBuf, String)>) -> Result<usize> {
        info!("📚 Bulk indexing {} documents (keyword-only)", documents.len());
        let start_time = std::time::Instant::now();

        let mut indexed_count = 0;
        let document_count = documents.len();
        for (path, content) in documents.iter() {
            if let Err(e) = self.storage.store_document(&path, &content).await {
                warn!("Failed to store document {}: {}", path.display(), e);
            } else {
                indexed_count += 1;
            }
        }

        let total_time = start_time.elapsed();
        info!("✅ Bulk indexing completed: {}/{} documents in {:.2}s",
              indexed_count, document_count, total_time.as_secs_f32());

        Ok(indexed_count)
    }

    /// Get search engine statistics
    pub async fn get_stats(&self) -> Result<HashMap<String, serde_json::Value>> {
        let mut stats = HashMap::new();

        // Storage stats
        if let Ok(storage_stats) = self.storage.get_stats().await {
            stats.insert("storage".to_string(), serde_json::to_value(storage_stats)?);
        }

        #[cfg(feature = "embeddings")]
        {
            // Model cache stats
            let cache_stats = self.model_cache.get_stats().await;
            stats.insert("model_cache".to_string(), serde_json::to_value(cache_stats)?);

            // Batch processor stats
            if let Some(batch_processor) = &self.batch_processor {
                let processor_stats = batch_processor.get_stats();
                stats.insert("batch_processor".to_string(), serde_json::to_value(processor_stats)?);
            }
        }

        Ok(stats)
    }

    /// Warm up the search engine by pre-loading models
    #[cfg(feature = "embeddings")]
    pub async fn warmup(&self) -> Result<()> {
        info!("🔥 Warming up search engine...");
        let start_time = std::time::Instant::now();

        // Pre-load embedding model
        let embedding_config = EmbeddingConfig {
            model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
            max_length: 512,
            normalize: true,
            batch_size: 32,
            device: "cpu".to_string(),
            cache_dir: None,
            model_params: HashMap::new(),
            use_quantization: false,
        };

        let _engine = self.model_cache.get_model(embedding_config).await?;

        // Generate a test embedding to warm up the model
        let test_embedding = _engine.embed_text("test query for warmup").await?;

        let warmup_time = start_time.elapsed();
        info!("🔥 Warmup completed in {:.2}s (embedding dimension: {})",
              warmup_time.as_secs_f32(), test_embedding.vector.len());

        Ok(())
    }

    /// Save vector store index to disk
    #[cfg(feature = "embeddings")]
    pub async fn save_vector_index(&self) -> Result<()> {
        let vector_store = self.vector_store.read().await;
        let index_path = self.config.index.path.join("vector_index");
        vector_store.save_index(&index_path).await?;
        info!("✅ Vector index saved to {:?}", index_path);
        Ok(())
    }

    /// Load vector store index from disk
    #[cfg(feature = "embeddings")]
    pub async fn load_vector_index(&self) -> Result<()> {
        let mut vector_store = self.vector_store.write().await;
        let index_path = self.config.index.path.join("vector_index");
        if index_path.exists() {
            vector_store.load_index(&index_path).await?;
            info!("✅ Vector index loaded from {:?}", index_path);
        } else {
            info!("📁 No existing vector index found at {:?}", index_path);
        }
        Ok(())
    }

    /// Get vector store performance metrics
    #[cfg(feature = "embeddings")]
    pub async fn get_vector_store_stats(&self) -> Result<VectorStoreStats> {
        let vector_store = self.vector_store.read().await;
        vector_store.get_stats().await
    }

    /// Clear vector store (for testing or reset)
    #[cfg(feature = "embeddings")]
    pub async fn clear_vector_index(&self) -> Result<()> {
        let mut vector_store = self.vector_store.write().await;
        vector_store.clear().await?;
        info!("🗑️ Vector index cleared");
        Ok(())
    }
}
