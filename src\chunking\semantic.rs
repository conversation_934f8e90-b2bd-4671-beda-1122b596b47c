use super::{
    <PERSON>ument<PERSON><PERSON><PERSON>, ChunkedDocument, EnhancedTextChunk, ChunkMetadata, ContentType,
    DocumentChunkingMetadata, ChunkBoundaries, estimate_token_count, count_words,
};
use crate::config::ChunkingConfig;
use crate::error::Result;
use crate::extraction::{ChunkType, ChunkPosition};
use async_trait::async_trait;
use std::collections::HashMap;
use std::path::Path;

pub struct SemanticChunker {
    sentence_splitter: SentenceSplitter,
}

impl SemanticChunker {
    pub fn new() -> Self {
        Self {
            sentence_splitter: SentenceSplitter::new(),
        }
    }

    fn split_into_sentences(&self, text: &str) -> Vec<Sentence> {
        self.sentence_splitter.split(text)
    }

    fn create_sentence_groups(&self, sentences: &[Sentence], group_size: usize) -> Vec<SentenceGroup> {
        let mut groups = Vec::new();
        
        for (i, sentence) in sentences.iter().enumerate() {
            let start = i.saturating_sub(group_size / 2);
            let end = (i + group_size / 2 + 1).min(sentences.len());
            
            let group_sentences = sentences[start..end].to_vec();
            let group_text = group_sentences.iter()
                .map(|s| s.text.as_str())
                .collect::<Vec<_>>()
                .join(" ");
            
            groups.push(SentenceGroup {
                anchor_index: i,
                sentences: group_sentences,
                text: group_text,
                embedding: None,
            });
        }
        
        groups
    }

    async fn compute_semantic_distances(&self, groups: &mut [SentenceGroup]) -> Result<Vec<f32>> {
        let mut distances = Vec::new();
        
        for i in 1..groups.len() {
            let distance = self.compute_cosine_distance(&groups[i-1].text, &groups[i].text).await?;
            distances.push(distance);
        }
        
        Ok(distances)
    }

    async fn compute_cosine_distance(&self, text1: &str, text2: &str) -> Result<f32> {
        Ok(0.5)
    }

    fn find_semantic_boundaries(&self, distances: &[f32], threshold: f32) -> Vec<usize> {
        let mut boundaries = vec![0];
        
        for (i, &distance) in distances.iter().enumerate() {
            if distance > threshold {
                boundaries.push(i + 1);
            }
        }
        
        if boundaries.last() != Some(&distances.len()) {
            boundaries.push(distances.len());
        }
        
        boundaries
    }

    fn create_semantic_chunks(
        &self,
        sentences: &[Sentence],
        boundaries: &[usize],
        config: &ChunkingConfig,
        file_path: &Path,
    ) -> Vec<EnhancedTextChunk> {
        let mut chunks = Vec::new();
        
        for i in 0..boundaries.len() - 1 {
            let start = boundaries[i];
            let end = boundaries[i + 1];
            
            if start >= sentences.len() || end > sentences.len() {
                continue;
            }
            
            let chunk_sentences = &sentences[start..end];
            let content = chunk_sentences.iter()
                .map(|s| s.text.as_str())
                .collect::<Vec<_>>()
                .join(" ");
            
            if content.trim().is_empty() {
                continue;
            }

            let token_count = estimate_token_count(&content);
            if token_count < config.min_chunk_size || token_count > config.max_chunk_size {
                continue;
            }

            let mut properties = HashMap::new();
            properties.insert("sentence_count".to_string(), chunk_sentences.len().to_string());
            properties.insert("semantic_boundary".to_string(), "true".to_string());

            let chunk_metadata = ChunkMetadata {
                source_file: file_path.to_string_lossy().to_string(),
                chunk_index: chunks.len(),
                total_chunks: 0,
                token_count,
                char_count: content.chars().count(),
                word_count: count_words(&content),
                language: None,
                content_type: ContentType::NaturalLanguage,
                semantic_score: Some(0.8),
                overlap_start: None,
                overlap_end: None,
                context_before: if start > 0 { Some(sentences[start - 1].text.clone()) } else { None },
                context_after: if end < sentences.len() { Some(sentences[end].text.clone()) } else { None },
                properties,
            };

            let boundaries = ChunkBoundaries {
                starts_at_sentence: true,
                ends_at_sentence: true,
                starts_at_paragraph: chunk_sentences.first().map(|s| s.starts_paragraph).unwrap_or(false),
                ends_at_paragraph: chunk_sentences.last().map(|s| s.ends_paragraph).unwrap_or(false),
                starts_at_code_block: false,
                ends_at_code_block: false,
                semantic_boundary_score: Some(0.8),
            };

            chunks.push(EnhancedTextChunk {
                content,
                chunk_type: ChunkType::Paragraph,
                position: ChunkPosition {
                    page: None,
                    line: chunk_sentences.first().map(|s| s.line_number),
                    column: Some(0),
                    offset: chunk_sentences.first().map(|s| s.offset),
                },
                metadata: chunk_metadata,
                boundaries,
            });
        }

        let total_chunks = chunks.len();
        for (i, chunk) in chunks.iter_mut().enumerate() {
            chunk.metadata.chunk_index = i;
            chunk.metadata.total_chunks = total_chunks;
        }
        
        chunks
    }
}

#[async_trait]
impl DocumentChunker for SemanticChunker {
    async fn chunk_document(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
    ) -> Result<ChunkedDocument> {
        let start_time = std::time::Instant::now();
        
        let sentences = self.split_into_sentences(content);
        let mut groups = self.create_sentence_groups(&sentences, 3);
        let distances = self.compute_semantic_distances(&mut groups).await?;
        let boundaries = self.find_semantic_boundaries(&distances, config.semantic_similarity_threshold);
        let chunks = self.create_semantic_chunks(&sentences, &boundaries, config, file_path);
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        let total_tokens: usize = chunks.iter().map(|c| c.metadata.token_count).sum();
        let total_chars: usize = chunks.iter().map(|c| c.metadata.char_count).sum();
        let avg_chunk_size = if chunks.is_empty() { 0.0 } else { total_tokens as f32 / chunks.len() as f32 };
        
        let variance = if chunks.len() > 1 {
            let mean = avg_chunk_size;
            let sum_sq_diff: f32 = chunks.iter()
                .map(|c| (c.metadata.token_count as f32 - mean).powi(2))
                .sum();
            (sum_sq_diff / chunks.len() as f32).sqrt()
        } else {
            0.0
        };

        let metadata = DocumentChunkingMetadata {
            total_chunks: chunks.len(),
            total_tokens,
            total_chars,
            chunking_strategy: "semantic".to_string(),
            processing_time_ms: processing_time,
            overlap_ratio: 0.0,
            average_chunk_size: avg_chunk_size,
            size_variance: variance,
            boundary_preservation_score: 0.9,
        };

        Ok(ChunkedDocument { chunks, metadata })
    }

    fn supports_content_type(&self, content_type: &ContentType) -> bool {
        matches!(content_type, ContentType::NaturalLanguage | ContentType::Mixed)
    }
    
    fn get_chunker_name(&self) -> &'static str {
        "semantic"
    }
    
    fn estimate_chunk_count(&self, content: &str, config: &ChunkingConfig) -> usize {
        let total_tokens = estimate_token_count(content);
        (total_tokens / config.max_tokens).max(1)
    }
    
    async fn chunk_streaming<F>(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
        chunk_callback: F,
    ) -> Result<DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync,
    {
        let chunked_doc = self.chunk_document(content, file_path, config).await?;
        
        for chunk in chunked_doc.chunks {
            if !chunk_callback(chunk) {
                break;
            }
        }
        
        Ok(chunked_doc.metadata)
    }
}

#[derive(Debug, Clone)]
struct Sentence {
    text: String,
    line_number: usize,
    offset: usize,
    starts_paragraph: bool,
    ends_paragraph: bool,
}

#[derive(Debug, Clone)]
struct SentenceGroup {
    anchor_index: usize,
    sentences: Vec<Sentence>,
    text: String,
    embedding: Option<Vec<f32>>,
}

struct SentenceSplitter;

impl SentenceSplitter {
    fn new() -> Self {
        Self
    }

    fn split(&self, text: &str) -> Vec<Sentence> {
        let mut sentences = Vec::new();
        let mut current_offset = 0;
        
        for (line_num, line) in text.lines().enumerate() {
            let line_sentences = self.split_line(line, line_num, current_offset);
            sentences.extend(line_sentences);
            current_offset += line.len() + 1;
        }
        
        sentences
    }

    fn split_line(&self, line: &str, line_number: usize, line_offset: usize) -> Vec<Sentence> {
        let mut sentences = Vec::new();
        let mut current_sentence = String::new();
        let mut sentence_start = line_offset;
        
        let chars: Vec<char> = line.chars().collect();
        let mut i = 0;
        
        while i < chars.len() {
            let ch = chars[i];
            current_sentence.push(ch);
            
            if matches!(ch, '.' | '!' | '?') {
                if i + 1 < chars.len() && chars[i + 1].is_whitespace() {
                    let sentence_text = current_sentence.trim().to_string();
                    if !sentence_text.is_empty() {
                        sentences.push(Sentence {
                            text: sentence_text,
                            line_number,
                            offset: sentence_start,
                            starts_paragraph: sentences.is_empty(),
                            ends_paragraph: false,
                        });
                    }
                    current_sentence.clear();
                    sentence_start = line_offset + i + 1;
                }
            }
            
            i += 1;
        }
        
        if !current_sentence.trim().is_empty() {
            sentences.push(Sentence {
                text: current_sentence.trim().to_string(),
                line_number,
                offset: sentence_start,
                starts_paragraph: sentences.is_empty(),
                ends_paragraph: true,
            });
        }
        
        if let Some(last) = sentences.last_mut() {
            last.ends_paragraph = true;
        }
        
        sentences
    }
}
