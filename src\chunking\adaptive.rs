use super::{
    Document<PERSON><PERSON>ker, ChunkedDocument, EnhancedTextChunk, ChunkMetadata, ContentType,
    DocumentChunkingMetadata, ChunkBoundaries, estimate_token_count, count_words,
    semantic::SemanticChunker, code_aware::CodeAwareChunker, sliding_window::SlidingWindowChunker,
};
use crate::config::ChunkingConfig;
use crate::error::Result;
use crate::extraction::{ChunkType, ChunkPosition};
use async_trait::async_trait;
use std::collections::HashMap;
use std::path::Path;

pub struct AdaptiveChunker {
    semantic_chunker: SemanticChunker,
    code_chunker: CodeAwareChunker,
    sliding_chunker: SlidingWindowChunker,
}

impl AdaptiveChunker {
    pub fn new() -> Self {
        Self {
            semantic_chunker: SemanticChunker::new(),
            code_chunker: CodeAwareChunker::new(),
            sliding_chunker: SlidingWindowChunker::new(),
        }
    }

    fn analyze_content(&self, content: &str, file_path: &Path) -> ContentAnalysis {
        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len();
        
        if total_lines == 0 {
            return ContentAnalysis {
                content_type: ContentType::Unknown,
                complexity_score: 0.0,
                structure_score: 0.0,
                recommended_strategy: ChunkingStrategy::SlidingWindow,
                confidence: 0.0,
            };
        }

        let mut code_indicators = 0;
        let mut natural_language_indicators = 0;
        let mut structure_indicators = 0;
        
        let sample_size = total_lines.min(100);
        
        for line in &lines[..sample_size] {
            let trimmed = line.trim();
            if trimmed.is_empty() {
                continue;
            }

            if self.is_code_line(trimmed) {
                code_indicators += 1;
            }
            
            if self.is_natural_language_line(trimmed) {
                natural_language_indicators += 1;
            }
            
            if self.is_structured_line(trimmed) {
                structure_indicators += 1;
            }
        }

        let code_ratio = code_indicators as f32 / sample_size as f32;
        let nl_ratio = natural_language_indicators as f32 / sample_size as f32;
        let structure_ratio = structure_indicators as f32 / sample_size as f32;

        let content_type = self.determine_content_type(code_ratio, nl_ratio, structure_ratio, file_path);
        let complexity_score = self.calculate_complexity_score(content, &lines);
        let structure_score = self.calculate_structure_score(&lines);
        
        let (recommended_strategy, confidence) = self.recommend_strategy(
            &content_type,
            complexity_score,
            structure_score,
            code_ratio,
            nl_ratio,
        );

        ContentAnalysis {
            content_type,
            complexity_score,
            structure_score,
            recommended_strategy,
            confidence,
        }
    }

    fn is_code_line(&self, line: &str) -> bool {
        line.starts_with("fn ") ||
        line.starts_with("def ") ||
        line.starts_with("function ") ||
        line.starts_with("class ") ||
        line.starts_with("struct ") ||
        line.starts_with("impl ") ||
        line.starts_with("use ") ||
        line.starts_with("import ") ||
        line.starts_with("from ") ||
        line.contains("let ") ||
        line.contains("const ") ||
        line.contains("var ") ||
        line.ends_with(";") ||
        line.ends_with("{") ||
        line.ends_with("}") ||
        line.starts_with("//") ||
        line.starts_with("#")
    }

    fn is_natural_language_line(&self, line: &str) -> bool {
        let word_count = line.split_whitespace().count();
        if word_count < 3 {
            return false;
        }

        let has_sentence_ending = line.ends_with('.') || line.ends_with('!') || line.ends_with('?');
        let has_articles = line.contains(" the ") || line.contains(" a ") || line.contains(" an ");
        let starts_with_capital = line.chars().next().map(|c| c.is_uppercase()).unwrap_or(false);
        
        (has_sentence_ending && starts_with_capital) || has_articles
    }

    fn is_structured_line(&self, line: &str) -> bool {
        line.starts_with("# ") ||
        line.starts_with("## ") ||
        line.starts_with("- ") ||
        line.starts_with("* ") ||
        line.starts_with("1. ") ||
        line.starts_with("> ") ||
        line.contains(": ") ||
        line.starts_with("```") ||
        line.starts_with("---")
    }

    fn determine_content_type(
        &self,
        code_ratio: f32,
        nl_ratio: f32,
        structure_ratio: f32,
        file_path: &Path,
    ) -> ContentType {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            match extension.to_lowercase().as_str() {
                "rs" | "py" | "js" | "ts" => return ContentType::Code(extension.to_string()),
                "md" | "txt" => {
                    if code_ratio > 0.3 {
                        return ContentType::Mixed;
                    } else if nl_ratio > 0.5 {
                        return ContentType::NaturalLanguage;
                    }
                }
                "json" | "yaml" | "yml" | "toml" => return ContentType::Structured(extension.to_string()),
                _ => {}
            }
        }

        if code_ratio > 0.6 {
            ContentType::Code("unknown".to_string())
        } else if nl_ratio > 0.6 {
            ContentType::NaturalLanguage
        } else if structure_ratio > 0.4 {
            ContentType::Structured("unknown".to_string())
        } else if code_ratio > 0.2 || nl_ratio > 0.2 {
            ContentType::Mixed
        } else {
            ContentType::Unknown
        }
    }

    fn calculate_complexity_score(&self, content: &str, lines: &[&str]) -> f32 {
        let avg_line_length = if lines.is_empty() {
            0.0
        } else {
            lines.iter().map(|l| l.len()).sum::<usize>() as f32 / lines.len() as f32
        };

        let nesting_depth = self.calculate_max_nesting_depth(lines);
        let vocabulary_diversity = self.calculate_vocabulary_diversity(content);
        
        let length_score = (avg_line_length / 100.0).min(1.0);
        let nesting_score = (nesting_depth as f32 / 10.0).min(1.0);
        let vocab_score = vocabulary_diversity;
        
        (length_score + nesting_score + vocab_score) / 3.0
    }

    fn calculate_max_nesting_depth(&self, lines: &[&str]) -> usize {
        let mut max_depth = 0;
        let mut current_depth = 0;
        
        for line in lines {
            let open_braces = line.matches('{').count();
            let close_braces = line.matches('}').count();
            
            current_depth += open_braces;
            max_depth = max_depth.max(current_depth);
            current_depth = current_depth.saturating_sub(close_braces);
        }
        
        max_depth
    }

    fn calculate_vocabulary_diversity(&self, content: &str) -> f32 {
        let words: Vec<&str> = content.split_whitespace().collect();
        if words.is_empty() {
            return 0.0;
        }

        let unique_words: std::collections::HashSet<&str> = words.iter().cloned().collect();
        unique_words.len() as f32 / words.len() as f32
    }

    fn calculate_structure_score(&self, lines: &[&str]) -> f32 {
        let mut structure_indicators = 0;
        
        for line in lines {
            let trimmed = line.trim();
            if trimmed.starts_with('#') ||
               trimmed.starts_with('-') ||
               trimmed.starts_with('*') ||
               trimmed.starts_with('>') ||
               trimmed.contains("::") ||
               trimmed.contains("->") {
                structure_indicators += 1;
            }
        }
        
        if lines.is_empty() {
            0.0
        } else {
            structure_indicators as f32 / lines.len() as f32
        }
    }

    fn recommend_strategy(
        &self,
        content_type: &ContentType,
        complexity_score: f32,
        structure_score: f32,
        code_ratio: f32,
        nl_ratio: f32,
    ) -> (ChunkingStrategy, f32) {
        match content_type {
            ContentType::Code(_) => {
                if complexity_score > 0.7 {
                    (ChunkingStrategy::CodeAware, 0.9)
                } else {
                    (ChunkingStrategy::SlidingWindow, 0.7)
                }
            }
            ContentType::NaturalLanguage => {
                if structure_score > 0.3 {
                    (ChunkingStrategy::Semantic, 0.8)
                } else {
                    (ChunkingStrategy::SlidingWindow, 0.6)
                }
            }
            ContentType::Structured(_) => {
                (ChunkingStrategy::SlidingWindow, 0.8)
            }
            ContentType::Mixed => {
                if code_ratio > nl_ratio {
                    (ChunkingStrategy::CodeAware, 0.6)
                } else {
                    (ChunkingStrategy::Semantic, 0.6)
                }
            }
            ContentType::Unknown => {
                (ChunkingStrategy::SlidingWindow, 0.5)
            }
        }
    }

    async fn chunk_with_strategy(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
        strategy: ChunkingStrategy,
    ) -> Result<ChunkedDocument> {
        match strategy {
            ChunkingStrategy::Semantic => {
                self.semantic_chunker.chunk_document(content, file_path, config).await
            }
            ChunkingStrategy::CodeAware => {
                self.code_chunker.chunk_document(content, file_path, config).await
            }
            ChunkingStrategy::SlidingWindow | ChunkingStrategy::FixedSize => {
                self.sliding_chunker.chunk_document(content, file_path, config).await
            }
            ChunkingStrategy::Adaptive => {
                let analysis = self.analyze_content(content, file_path);
                self.chunk_with_strategy(content, file_path, config, analysis.recommended_strategy).await
            }
        }
    }
}

#[async_trait]
impl DocumentChunker for AdaptiveChunker {
    async fn chunk_document(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
    ) -> Result<ChunkedDocument> {
        let analysis = self.analyze_content(content, file_path);
        
        let strategy = analysis.recommended_strategy.clone();
        let mut result = self.chunk_with_strategy(
            content,
            file_path,
            config,
            strategy.clone(),
        ).await?;

        result.metadata.chunking_strategy = format!(
            "adaptive({})",
            match strategy {
                ChunkingStrategy::Semantic => "semantic",
                ChunkingStrategy::CodeAware => "code_aware",
                ChunkingStrategy::SlidingWindow => "sliding_window",
                ChunkingStrategy::FixedSize => "fixed_size",
                ChunkingStrategy::Adaptive => "adaptive",
            }
        );

        for chunk in &mut result.chunks {
            chunk.metadata.properties.insert(
                "adaptive_confidence".to_string(),
                analysis.confidence.to_string(),
            );
            chunk.metadata.properties.insert(
                "complexity_score".to_string(),
                analysis.complexity_score.to_string(),
            );
            chunk.metadata.properties.insert(
                "structure_score".to_string(),
                analysis.structure_score.to_string(),
            );
        }

        Ok(result)
    }

    fn supports_content_type(&self, _content_type: &ContentType) -> bool {
        true
    }
    
    fn get_chunker_name(&self) -> &'static str {
        "adaptive"
    }
    
    fn estimate_chunk_count(&self, content: &str, config: &ChunkingConfig) -> usize {
        let total_tokens = estimate_token_count(content);
        (total_tokens / config.max_tokens).max(1)
    }
    
    async fn chunk_streaming<F>(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
        chunk_callback: F,
    ) -> Result<DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync,
    {
        let analysis = self.analyze_content(content, file_path);
        
        match analysis.recommended_strategy {
            ChunkingStrategy::Semantic => {
                self.semantic_chunker.chunk_streaming(content, file_path, config, chunk_callback).await
            }
            ChunkingStrategy::CodeAware => {
                self.code_chunker.chunk_streaming(content, file_path, config, chunk_callback).await
            }
            ChunkingStrategy::SlidingWindow | ChunkingStrategy::FixedSize => {
                self.sliding_chunker.chunk_streaming(content, file_path, config, chunk_callback).await
            }
            ChunkingStrategy::Adaptive => {
                self.sliding_chunker.chunk_streaming(content, file_path, config, chunk_callback).await
            }
        }
    }
}

#[derive(Debug, Clone)]
struct ContentAnalysis {
    content_type: ContentType,
    complexity_score: f32,
    structure_score: f32,
    recommended_strategy: ChunkingStrategy,
    confidence: f32,
}

#[derive(Debug, Clone)]
enum ChunkingStrategy {
    Semantic,
    CodeAware,
    SlidingWindow,
    FixedSize,
    Adaptive,
}
