use crate::error::Result;
use crate::indexing::IndexingConfig;
use crossbeam_channel::{Receiver, Sender};
use notify::{Config, Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WatcherConfig {
    pub debounce_ms: u64,
    pub ignore_patterns: Vec<String>,
    pub include_patterns: Vec<String>,
    pub max_events_per_second: usize,
    pub enable_recursive: bool,
    pub poll_interval_ms: Option<u64>,
}

impl From<&IndexingConfig> for WatcherConfig {
    fn from(config: &IndexingConfig) -> Self {
        Self {
            debounce_ms: config.debounce_ms,
            ignore_patterns: vec![
                "*.tmp".to_string(),
                "*.swp".to_string(),
                "*.lock".to_string(),
                ".git/**".to_string(),
                "node_modules/**".to_string(),
                "target/**".to_string(),
            ],
            include_patterns: vec![
                "*.rs".to_string(),
                "*.py".to_string(),
                "*.js".to_string(),
                "*.ts".to_string(),
                "*.md".to_string(),
                "*.txt".to_string(),
                "*.json".to_string(),
                "*.yaml".to_string(),
                "*.yml".to_string(),
                "*.toml".to_string(),
            ],
            max_events_per_second: 1000,
            enable_recursive: true,
            poll_interval_ms: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WatchEvent {
    pub path: PathBuf,
    pub kind: WatchEventKind,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub size: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WatchEventKind {
    Created,
    Modified,
    Deleted,
    Renamed { from: PathBuf, to: PathBuf },
    Other(String),
}

pub struct FileWatcher {
    config: WatcherConfig,
    watcher: Option<RecommendedWatcher>,
    event_sender: mpsc::UnboundedSender<WatchEvent>,
    event_receiver: Arc<Mutex<mpsc::UnboundedReceiver<WatchEvent>>>,
    debounce_map: Arc<Mutex<HashMap<PathBuf, Instant>>>,
    rate_limiter: Arc<Mutex<RateLimiter>>,
    watched_paths: Arc<Mutex<HashSet<PathBuf>>>,
    is_running: Arc<std::sync::atomic::AtomicBool>,
}

struct RateLimiter {
    events_this_second: usize,
    last_reset: Instant,
    max_events_per_second: usize,
}

impl RateLimiter {
    fn new(max_events_per_second: usize) -> Self {
        Self {
            events_this_second: 0,
            last_reset: Instant::now(),
            max_events_per_second,
        }
    }

    fn should_allow(&mut self) -> bool {
        let now = Instant::now();
        
        // Reset counter every second
        if now.duration_since(self.last_reset) >= Duration::from_secs(1) {
            self.events_this_second = 0;
            self.last_reset = now;
        }

        if self.events_this_second < self.max_events_per_second {
            self.events_this_second += 1;
            true
        } else {
            false
        }
    }
}

impl FileWatcher {
    pub fn new(config: WatcherConfig) -> Result<Self> {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        let event_receiver = Arc::new(Mutex::new(event_receiver));

        Ok(Self {
            config: config.clone(),
            watcher: None,
            event_sender,
            event_receiver,
            debounce_map: Arc::new(Mutex::new(HashMap::new())),
            rate_limiter: Arc::new(Mutex::new(RateLimiter::new(config.max_events_per_second))),
            watched_paths: Arc::new(Mutex::new(HashSet::new())),
            is_running: Arc::new(std::sync::atomic::AtomicBool::new(false)),
        })
    }

    pub async fn start_watching(&mut self, paths: Vec<&Path>) -> Result<()> {
        let (tx, rx): (Sender<notify::Result<Event>>, Receiver<notify::Result<Event>>) = 
            crossbeam_channel::unbounded();

        // Configure notify watcher
        let mut watcher_config = Config::default()
            .with_poll_interval(Duration::from_millis(
                self.config.poll_interval_ms.unwrap_or(1000)
            ));

        let watcher = RecommendedWatcher::new(
            move |res| {
                if let Err(e) = tx.send(res) {
                    tracing::error!("Failed to send watch event: {}", e);
                }
            },
            watcher_config,
        )?;

        self.watcher = Some(watcher);

        // Add paths to watch
        for path in paths {
            self.add_watch_path(path).await?;
        }

        // Start event processing task
        self.start_event_processor(rx).await;

        self.is_running.store(true, std::sync::atomic::Ordering::Relaxed);
        tracing::info!("File watcher started for {} paths", self.watched_paths.lock().await.len());

        Ok(())
    }

    async fn add_watch_path(&mut self, path: &Path) -> Result<()> {
        if let Some(ref mut watcher) = self.watcher {
            let mode = if self.config.enable_recursive {
                RecursiveMode::Recursive
            } else {
                RecursiveMode::NonRecursive
            };

            watcher.watch(path, mode)?;
            
            let mut watched_paths = self.watched_paths.lock().await;
            watched_paths.insert(path.to_path_buf());
            
            tracing::debug!("Added watch path: {}", path.display());
        }

        Ok(())
    }

    async fn start_event_processor(&self, rx: Receiver<notify::Result<Event>>) {
        let event_sender = self.event_sender.clone();
        let debounce_map = self.debounce_map.clone();
        let rate_limiter = self.rate_limiter.clone();
        let config = self.config.clone();
        let is_running = self.is_running.clone();

        tokio::spawn(async move {
            let mut debounce_cleanup_interval = tokio::time::interval(
                Duration::from_millis(config.debounce_ms * 2)
            );

            loop {
                tokio::select! {
                    // Process incoming events
                    _ = async {
                        while let Ok(event_result) = rx.try_recv() {
                            match event_result {
                                Ok(event) => {
                                    if let Err(e) = Self::process_notify_event(
                                        event,
                                        &event_sender,
                                        &debounce_map,
                                        &rate_limiter,
                                        &config,
                                    ).await {
                                        tracing::error!("Failed to process watch event: {}", e);
                                    }
                                }
                                Err(e) => {
                                    tracing::error!("Watch error: {}", e);
                                }
                            }
                        }
                    } => {}

                    // Cleanup old debounce entries
                    _ = debounce_cleanup_interval.tick() => {
                        Self::cleanup_debounce_map(&debounce_map, &config).await;
                    }

                    // Check if we should stop
                    _ = tokio::time::sleep(Duration::from_millis(100)) => {
                        if !is_running.load(std::sync::atomic::Ordering::Relaxed) {
                            break;
                        }
                    }
                }
            }
        });
    }

    async fn process_notify_event(
        event: Event,
        event_sender: &mpsc::UnboundedSender<WatchEvent>,
        debounce_map: &Arc<Mutex<HashMap<PathBuf, Instant>>>,
        rate_limiter: &Arc<Mutex<RateLimiter>>,
        config: &WatcherConfig,
    ) -> Result<()> {
        // Rate limiting
        {
            let mut limiter = rate_limiter.lock().await;
            if !limiter.should_allow() {
                return Ok(()); // Drop event due to rate limiting
            }
        }

        for path in event.paths {
            // Skip if path should be ignored
            if Self::should_ignore_path(&path, config) {
                continue;
            }

            // Debouncing
            {
                let mut debounce = debounce_map.lock().await;
                let now = Instant::now();
                
                if let Some(last_event) = debounce.get(&path) {
                    if now.duration_since(*last_event) < Duration::from_millis(config.debounce_ms) {
                        continue; // Skip due to debouncing
                    }
                }
                
                debounce.insert(path.clone(), now);
            }

            // Convert notify event to our event type
            let watch_event = Self::convert_event_kind(&event.kind, path.clone())?;

            // Send event
            if let Err(e) = event_sender.send(watch_event) {
                tracing::error!("Failed to send watch event: {}", e);
            }
        }

        Ok(())
    }

    fn should_ignore_path(path: &Path, config: &WatcherConfig) -> bool {
        let path_str = path.to_string_lossy();

        // Check ignore patterns
        for pattern in &config.ignore_patterns {
            if glob_match::glob_match(pattern, &path_str) {
                return true;
            }
        }

        // Check include patterns (if any)
        if !config.include_patterns.is_empty() {
            let mut matches_include = false;
            for pattern in &config.include_patterns {
                if glob_match::glob_match(pattern, &path_str) {
                    matches_include = true;
                    break;
                }
            }
            if !matches_include {
                return true;
            }
        }

        false
    }

    fn convert_event_kind(kind: &EventKind, path: PathBuf) -> Result<WatchEvent> {
        let timestamp = chrono::Utc::now();
        let size = std::fs::metadata(&path).ok().map(|m| m.len());

        let watch_kind = match kind {
            EventKind::Create(_) => WatchEventKind::Created,
            EventKind::Modify(_) => WatchEventKind::Modified,
            EventKind::Remove(_) => WatchEventKind::Deleted,
            EventKind::Other => WatchEventKind::Other("other".to_string()),
            _ => WatchEventKind::Other(format!("{:?}", kind)),
        };

        Ok(WatchEvent {
            path,
            kind: watch_kind,
            timestamp,
            size,
        })
    }

    async fn cleanup_debounce_map(
        debounce_map: &Arc<Mutex<HashMap<PathBuf, Instant>>>,
        config: &WatcherConfig,
    ) {
        let mut debounce = debounce_map.lock().await;
        let now = Instant::now();
        let cleanup_threshold = Duration::from_millis(config.debounce_ms * 5);

        debounce.retain(|_, last_event| {
            now.duration_since(*last_event) < cleanup_threshold
        });
    }

    pub async fn next_change(&self) -> Option<WatchEvent> {
        let mut receiver = self.event_receiver.lock().await;
        receiver.recv().await
    }

    pub async fn stop(&mut self) -> Result<()> {
        self.is_running.store(false, std::sync::atomic::Ordering::Relaxed);

        if let Some(mut watcher) = self.watcher.take() {
            // Remove all watched paths
            let watched_paths = self.watched_paths.lock().await;
            for path in watched_paths.iter() {
                let _ = watcher.unwatch(path);
            }
        }

        tracing::info!("File watcher stopped");
        Ok(())
    }

    pub async fn get_watched_paths(&self) -> Vec<PathBuf> {
        let watched_paths = self.watched_paths.lock().await;
        watched_paths.iter().cloned().collect()
    }
}

// Simple glob matching implementation
mod glob_match {
    pub fn glob_match(pattern: &str, text: &str) -> bool {
        // Simple implementation - in production, use a proper glob library
        if pattern.contains("**") {
            let parts: Vec<&str> = pattern.split("**").collect();
            if parts.len() == 2 {
                let prefix = parts[0];
                let suffix = parts[1];
                return text.starts_with(prefix) && text.ends_with(suffix);
            }
        }
        
        if pattern.contains('*') {
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.len() == 2 {
                let prefix = parts[0];
                let suffix = parts[1];
                return text.starts_with(prefix) && text.ends_with(suffix);
            }
        }
        
        pattern == text
    }
}
