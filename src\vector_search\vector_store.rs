use super::*;
use anyhow::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorStoreConfig {
    
    pub dimension: usize,
    
    pub distance_metric: DistanceMetric,
    
    pub max_vectors: Option<usize>,
    
    pub enable_mmap: bool,
    
    pub persistence_path: Option<PathBuf>,
    
    pub enable_quantization: bool,
    
    pub quantization_config: Option<QuantizationConfig>,
    
    pub hnsw_config: HnswConfig,
    
    pub cache_config: CacheConfig,
}

impl Default for VectorStoreConfig {
    fn default() -> Self {
        Self {
            dimension: 384,
            distance_metric: DistanceMetric::Cosine,
            max_vectors: None,
            enable_mmap: true,
            persistence_path: None,
            enable_quantization: false,
            quantization_config: None,
            hnsw_config: HnswConfig::default(),
            cache_config: CacheConfig::default(),
        }
    }
}


#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SearchQuery {
    
    pub vector: Vector,
    
    pub config: SearchConfig,
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    
    pub id: VectorId,
    
    pub distance: f32,
    
    pub similarity: f32,
    
    pub metadata: VectorMetadata,
    
    pub vector: Option<Vector>,
}


#[async_trait]
pub trait VectorStore: Send + Sync {
    
    async fn add_vector(&mut self, entry: VectorEntry) -> Result<()>;
    
    
    async fn add_vectors(&mut self, entries: Vec<VectorEntry>) -> Result<BatchResult>;
    
    
    async fn search(&self, query: &SearchQuery) -> Result<Vec<SearchResult>>;
    
    
    async fn range_search(&self, query: &SearchQuery, max_distance: f32) -> Result<Vec<SearchResult>>;
    
    
    async fn get_vector(&self, id: VectorId) -> Result<Option<VectorEntry>>;
    
    
    async fn update_metadata(&mut self, id: VectorId, metadata: VectorMetadata) -> Result<()>;
    
    
    async fn remove_vector(&mut self, id: VectorId) -> Result<bool>;
    
    
    async fn get_stats(&self) -> Result<VectorStoreStats>;
    
    
    async fn build_index(&mut self, progress_callback: Option<ProgressCallback>) -> Result<()>;
    
    
    async fn save_index(&self, path: &PathBuf) -> Result<()>;
    
    
    async fn load_index(&mut self, path: &PathBuf) -> Result<()>;
    
    
    async fn clear(&mut self) -> Result<()>;
    
    
    fn get_config(&self) -> &VectorStoreConfig;
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorStoreStats {
    
    pub total_vectors: usize,
    
    pub index_size_bytes: usize,
    
    pub memory_usage_bytes: usize,
    
    pub avg_search_time_ms: f32,
    
    pub index_build_time_ms: Option<u64>,
    
    pub cache_hit_rate: f32,
    
    pub compression_ratio: Option<f32>,
}


pub struct HnswVectorStore {
    config: VectorStoreConfig,
    index: HnswIndex,
    vectors: HashMap<VectorId, VectorEntry>,
    quantizer: Option<VectorQuantizer>,
    persistence: Option<IndexPersistence>,
    query_cache: QueryCache,
    metrics: SearchMetrics,
    next_id: VectorId,
}

impl HnswVectorStore {
    
    pub fn new(config: VectorStoreConfig) -> Result<Self> {
        let index = HnswIndex::new(config.hnsw_config.clone(), config.distance_metric)?;
        let quantizer = if config.enable_quantization {
            Some(VectorQuantizer::new(config.quantization_config.clone().unwrap_or_default())?)
        } else {
            None
        };
        
        let persistence = if let Some(ref path) = config.persistence_path {
            Some(IndexPersistence::new(PersistenceConfig {
                base_path: path.clone(),
                enable_compression: true,
                backup_count: 3,
            })?)
        } else {
            None
        };
        
        Ok(Self {
            query_cache: QueryCache::new(config.cache_config.clone()),
            metrics: SearchMetrics::new(),
            index,
            vectors: HashMap::new(),
            quantizer,
            persistence,
            config,
            next_id: 1,
        })
    }
    
    
    pub async fn load(config: VectorStoreConfig, path: &PathBuf) -> Result<Self> {
        let mut store = Self::new(config)?;
        store.load_index(path).await?;
        Ok(store)
    }
    
    
    fn get_next_id(&mut self) -> VectorId {
        let id = self.next_id;
        self.next_id += 1;
        id
    }
    
    
    fn validate_vector(&self, vector: &Vector) -> Result<()> {
        if vector.len() != self.config.dimension {
            return Err(anyhow::anyhow!(
                "Vector dimension {} does not match expected dimension {}",
                vector.len(),
                self.config.dimension
            ));
        }
        Ok(())
    }
    
    
    fn quantize_vector(&self, vector: &Vector) -> Result<Vector> {
        if let Some(ref quantizer) = self.quantizer {
            quantizer.quantize(vector)
        } else {
            Ok(vector.clone())
        }
    }
    
    
    fn check_cache(&self, query: &SearchQuery) -> Option<Vec<SearchResult>> {
        if query.config.enable_cache {
            self.query_cache.get(query)
        } else {
            None
        }
    }
    
    
    fn cache_result(&self, query: &SearchQuery, results: &[SearchResult]) {
        if query.config.enable_cache {
            self.query_cache.put(query.clone(), results.to_vec());
        }
    }
}

#[async_trait]
impl VectorStore for HnswVectorStore {
    async fn add_vector(&mut self, mut entry: VectorEntry) -> Result<()> {
        self.validate_vector(&entry.vector)?;
        
        
        if entry.id == 0 {
            entry.id = self.get_next_id();
        }
        
        
        let quantized_vector = self.quantize_vector(&entry.vector)?;
        
        
        self.index.add_vector(entry.id, &quantized_vector).await?;
        
        
        self.vectors.insert(entry.id, entry);
        
        
        self.query_cache.clear();
        
        Ok(())
    }
    
    async fn add_vectors(&mut self, entries: Vec<VectorEntry>) -> Result<BatchResult> {
        let start_time = std::time::Instant::now();
        let mut successful = Vec::new();
        let mut failed = Vec::new();
        
        for mut entry in entries {
            
            if entry.id == 0 {
                entry.id = self.get_next_id();
            }
            
            match VectorStore::add_vector(self, entry.clone()).await {
                Ok(_) => successful.push(entry.id),
                Err(e) => failed.push((entry.id, e.to_string())),
            }
        }
        
        
        if !successful.is_empty() {
            self.build_index(None).await?;
        }
        
        Ok(BatchResult {
            successful,
            failed,
            total_time_ms: start_time.elapsed().as_millis() as u64,
        })
    }
    
    async fn search(&self, query: &SearchQuery) -> Result<Vec<SearchResult>> {
        
        if let Some(cached_results) = self.check_cache(query) {
            self.metrics.record_cache_hit();
            return Ok(cached_results);
        }
        
        let start_time = std::time::Instant::now();
        self.validate_vector(&query.vector)?;
        
        
        let quantized_query = self.quantize_vector(&query.vector)?;
        
        
        let index_results = self.index.search(&quantized_query, &query.config).await?;
        
        
        let mut results = Vec::new();
        for (id, distance) in index_results {
            if let Some(entry) = self.vectors.get(&id) {
                let similarity = similarity::distance_to_similarity(distance, self.config.distance_metric);
                results.push(SearchResult {
                    id,
                    distance,
                    similarity,
                    metadata: entry.metadata.clone(),
                    vector: None, 
                });
            }
        }
        
        
        if let Some(max_distance) = query.config.max_distance {
            results.retain(|r| r.distance <= max_distance);
        }
        
        
        let search_time = start_time.elapsed().as_millis() as f32;
        self.metrics.record_search(search_time, results.len());
        
        
        self.cache_result(query, &results);
        
        Ok(results)
    }
    
    async fn range_search(&self, query: &SearchQuery, max_distance: f32) -> Result<Vec<SearchResult>> {
        let mut range_query = query.clone();
        range_query.config.max_distance = Some(max_distance);
        range_query.config.k = self.vectors.len(); 
        
        self.search(&range_query).await
    }
    
    async fn get_vector(&self, id: VectorId) -> Result<Option<VectorEntry>> {
        Ok(self.vectors.get(&id).cloned())
    }
    
    async fn update_metadata(&mut self, id: VectorId, metadata: VectorMetadata) -> Result<()> {
        if let Some(entry) = self.vectors.get_mut(&id) {
            entry.metadata = metadata;
            Ok(())
        } else {
            Err(anyhow::anyhow!("Vector with ID {} not found", id))
        }
    }
    
    async fn remove_vector(&mut self, id: VectorId) -> Result<bool> {
        if self.vectors.remove(&id).is_some() {
            self.index.remove_vector(id).await?;
            self.query_cache.clear();
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    async fn get_stats(&self) -> Result<VectorStoreStats> {
        let index_stats = self.index.get_stats().await?;
        let metrics_stats = self.metrics.get_stats();
        
        Ok(VectorStoreStats {
            total_vectors: self.vectors.len(),
            index_size_bytes: index_stats.memory_usage_bytes,
            memory_usage_bytes: index_stats.memory_usage_bytes + 
                (self.vectors.len() * std::mem::size_of::<VectorEntry>()),
            avg_search_time_ms: metrics_stats.avg_search_time_ms,
            index_build_time_ms: index_stats.build_time_ms,
            cache_hit_rate: metrics_stats.cache_hit_rate,
            compression_ratio: self.quantizer.as_ref().map(|q| q.get_compression_ratio()),
        })
    }
    
    async fn build_index(&mut self, progress_callback: Option<ProgressCallback>) -> Result<()> {
        let vectors: Vec<(VectorId, Vector)> = self.vectors
            .iter()
            .map(|(id, entry)| {
                let quantized = self.quantize_vector(&entry.vector).unwrap_or_else(|_| entry.vector.clone());
                (*id, quantized)
            })
            .collect();
        
        self.index.build(&vectors, progress_callback).await?;
        self.query_cache.clear();
        Ok(())
    }
    
    async fn save_index(&self, path: &PathBuf) -> Result<()> {
        if let Some(ref persistence) = self.persistence {
            persistence.save_index(&self.index, &self.vectors, path).await
        } else {
            Err(anyhow::anyhow!("Persistence not configured"))
        }
    }
    
    async fn load_index(&mut self, path: &PathBuf) -> Result<()> {
        if let Some(ref persistence) = self.persistence {
            let (index, vectors) = persistence.load_index(path).await?;
            self.index = index;
            self.vectors = vectors;
            
            
            self.next_id = self.vectors.keys().max().unwrap_or(&0) + 1;
            
            Ok(())
        } else {
            Err(anyhow::anyhow!("Persistence not configured"))
        }
    }
    
    async fn clear(&mut self) -> Result<()> {
        self.vectors.clear();
        self.index.clear().await?;
        self.query_cache.clear();
        self.next_id = 1;
        Ok(())
    }
    
    fn get_config(&self) -> &VectorStoreConfig {
        &self.config
    }
}

impl HnswVectorStore {
    /// Add a vector with string ID (convenience method for indexing system)
    pub async fn add_vector(&mut self, id: String, vector: Vec<f32>) -> Result<()> {
        let vector_id = self.get_next_id();
        let mut labels = std::collections::HashMap::new();
        labels.insert("document_id".to_string(), id);

        let entry = VectorEntry {
            id: vector_id,
            vector,
            metadata: VectorMetadata {
                labels,
                created_at: chrono::Utc::now(),
                payload: None,
            },
        };

        VectorStore::add_vector(self, entry).await
    }

    /// Remove vector by string ID (convenience method for indexing system)
    pub async fn remove_vector(&mut self, document_id: &str) -> Result<bool> {
        // Find vector by document ID
        let vector_id = self.vectors
            .iter()
            .find(|(_, entry)| {
                entry.metadata.labels.get("document_id") == Some(&document_id.to_string())
            })
            .map(|(id, _)| *id);

        if let Some(id) = vector_id {
            VectorStore::remove_vector(self, id).await
        } else {
            Ok(false)
        }
    }

    /// Flush pending operations (convenience method for indexing system)
    pub async fn flush(&self) -> Result<()> {
        // Vector store operations are typically immediate, but we can save index if needed
        if let Some(ref path) = self.config.persistence_path {
            self.save_index(path).await?;
        }
        Ok(())
    }
}
