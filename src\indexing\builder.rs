use crate::error::Result;
use crate::indexing::IndexingConfig;
use crate::storage::Storage;
use crate::search::SearchEngine;
use async_trait::async_trait;
use dashmap::DashMap;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::sync::{Mutex, Semaphore};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentMetadata {
    pub file_size: u64,
    pub modified_time: chrono::DateTime<chrono::Utc>,
    pub content_hash: String,
    pub file_type: String,
    pub encoding: Option<String>,
    pub chunk_count: usize,
}

#[derive(Debug, Clone)]
pub enum IndexOperation {
    Update {
        path: PathBuf,
        content: String,
        metadata: DocumentMetadata,
    },
    Delete {
        path: PathBuf,
    },
    Move {
        from: PathBuf,
        to: PathBuf,
    },
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct IndexTransaction {
    pub id: String,
    pub operations: Vec<IndexOperation>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub timeout_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone)]
pub struct TransactionLog {
    pub transaction_id: String,
    pub operation_type: String,
    pub path: PathBuf,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub status: TransactionStatus,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    RolledBack,
}

pub struct IndexBuilder {
    storage: Arc<Storage>,
    search_engine: Arc<SearchEngine>,
    active_transactions: Arc<DashMap<String, IndexTransaction>>,
    transaction_logs: Arc<Mutex<Vec<TransactionLog>>>,
    operation_semaphore: Arc<Semaphore>,
    config: IndexingConfig,
    stats: Arc<IndexingStats>,
    checkpoint_counter: AtomicU64,
}

#[derive(Debug, Default)]
pub struct IndexingStats {
    pub total_operations: AtomicU64,
    pub successful_operations: AtomicU64,
    pub failed_operations: AtomicU64,
    pub documents_indexed: AtomicU64,
    pub documents_deleted: AtomicU64,
    pub bytes_processed: AtomicU64,
    pub last_checkpoint: RwLock<Option<chrono::DateTime<chrono::Utc>>>,
}

impl IndexBuilder {
    pub async fn new(index_path: &Path, config: &IndexingConfig) -> Result<Self> {
        let storage = Arc::new(Storage::new(index_path).await?);
        let search_config = crate::config::Config::default();
        let search_engine = Arc::new(SearchEngine::new(&search_config).await?);

        let operation_semaphore = Arc::new(Semaphore::new(config.max_concurrent_operations));

        Ok(Self {
            storage,
            search_engine,
            active_transactions: Arc::new(DashMap::new()),
            transaction_logs: Arc::new(Mutex::new(Vec::new())),
            operation_semaphore,
            config: config.clone(),
            stats: Arc::new(IndexingStats::default()),
            checkpoint_counter: AtomicU64::new(0),
        })
    }

    pub async fn begin_transaction(&self) -> Result<IndexTransaction> {
        let transaction_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();
        let timeout_at = now + chrono::Duration::milliseconds(self.config.transaction_timeout_ms as i64);

        let transaction = IndexTransaction {
            id: transaction_id.clone(),
            operations: Vec::new(),
            created_at: now,
            timeout_at,
        };

        self.active_transactions.insert(transaction_id, transaction.clone());
        
        tracing::debug!("Started transaction: {}", transaction.id);
        Ok(transaction)
    }

    pub async fn execute_batch(
        &self,
        transaction: &IndexTransaction,
        operations: Vec<IndexOperation>,
    ) -> Result<()> {
        let _permit = self.operation_semaphore.acquire().await?;

        // Check transaction timeout
        if chrono::Utc::now() > transaction.timeout_at {
            return Err(anyhow::anyhow!("Transaction {} timed out", transaction.id));
        }

        // Process operations in parallel chunks
        let chunk_size = self.config.batch_size.min(operations.len());
        let chunks: Vec<_> = operations.chunks(chunk_size).collect();

        for (chunk_idx, chunk) in chunks.iter().enumerate() {
            let futures: Vec<_> = chunk.iter().enumerate().map(|(op_idx, operation)| {
                let operation_id = format!("{}-{}-{}", transaction.id, chunk_idx, op_idx);
                self.execute_operation(operation.clone(), operation_id)
            }).collect();

            // Execute chunk operations concurrently
            let results = futures::future::join_all(futures).await;

            // Check for failures
            for result in results {
                if let Err(e) = result {
                    self.stats.failed_operations.fetch_add(1, Ordering::Relaxed);
                    return Err(e);
                }
            }
        }

        self.stats.total_operations.fetch_add(operations.len() as u64, Ordering::Relaxed);
        self.stats.successful_operations.fetch_add(operations.len() as u64, Ordering::Relaxed);

        // Checkpoint if needed
        let checkpoint_count = self.checkpoint_counter.fetch_add(1, Ordering::Relaxed);
        if checkpoint_count % (self.config.checkpoint_interval_ms / self.config.debounce_ms) == 0 {
            self.create_checkpoint().await?;
        }

        Ok(())
    }

    async fn execute_operation(&self, operation: IndexOperation, operation_id: String) -> Result<()> {
        let start_time = std::time::Instant::now();
        
        let log_entry = TransactionLog {
            transaction_id: operation_id.clone(),
            operation_type: match &operation {
                IndexOperation::Update { .. } => "update".to_string(),
                IndexOperation::Delete { .. } => "delete".to_string(),
                IndexOperation::Move { .. } => "move".to_string(),
            },
            path: match &operation {
                IndexOperation::Update { path, .. } => path.clone(),
                IndexOperation::Delete { path } => path.clone(),
                IndexOperation::Move { from, .. } => from.clone(),
            },
            timestamp: chrono::Utc::now(),
            status: TransactionStatus::InProgress,
            error: None,
        };

        // Log operation start
        {
            let mut logs = self.transaction_logs.lock().await;
            logs.push(log_entry.clone());
        }

        let result = match operation {
            IndexOperation::Update { path, content, metadata } => {
                self.handle_update(path, content, metadata).await
            }
            IndexOperation::Delete { path } => {
                self.handle_delete(path).await
            }
            IndexOperation::Move { from, to } => {
                self.handle_move(from, to).await
            }
        };

        // Update log with result
        {
            let mut logs = self.transaction_logs.lock().await;
            if let Some(log) = logs.iter_mut().find(|l| l.transaction_id == operation_id) {
                match &result {
                    Ok(_) => {
                        log.status = TransactionStatus::Completed;
                    }
                    Err(e) => {
                        log.status = TransactionStatus::Failed;
                        log.error = Some(e.to_string());
                    }
                }
            }
        }

        let duration = start_time.elapsed();
        tracing::debug!("Operation {} completed in {:?}", operation_id, duration);

        result
    }

    async fn handle_update(&self, path: PathBuf, content: String, metadata: DocumentMetadata) -> Result<()> {
        // Store document content and metadata
        self.storage.store_document(&path, &content, &metadata).await?;

        // Update search index
        self.search_engine.index_document(&path, &content).await?;

        self.stats.documents_indexed.fetch_add(1, Ordering::Relaxed);
        self.stats.bytes_processed.fetch_add(content.len() as u64, Ordering::Relaxed);

        tracing::debug!("Updated document: {}", path.display());
        Ok(())
    }

    async fn handle_delete(&self, path: PathBuf) -> Result<()> {
        // Remove from storage
        self.storage.delete_document(&path).await?;

        // Remove from search index
        self.search_engine.remove_document(&path).await?;

        self.stats.documents_deleted.fetch_add(1, Ordering::Relaxed);

        tracing::debug!("Deleted document: {}", path.display());
        Ok(())
    }

    async fn handle_move(&self, from: PathBuf, to: PathBuf) -> Result<()> {
        // Get existing document
        if let Some((content, metadata)) = self.storage.get_document(&from).await? {
            // Store at new location
            self.storage.store_document(&to, &content, &metadata).await?;
            
            // Update search index
            self.search_engine.remove_document(&from).await?;
            self.search_engine.index_document(&to, &content).await?;
            
            // Remove old location
            self.storage.delete_document(&from).await?;

            tracing::debug!("Moved document: {} -> {}", from.display(), to.display());
        }

        Ok(())
    }

    pub async fn commit_transaction(&self, transaction: IndexTransaction) -> Result<()> {
        // Flush any pending operations
        self.search_engine.flush().await?;
        self.storage.flush().await?;

        // Remove from active transactions
        self.active_transactions.remove(&transaction.id);

        tracing::debug!("Committed transaction: {}", transaction.id);
        Ok(())
    }

    pub async fn rollback_transaction(&self, transaction: IndexTransaction) -> Result<()> {
        // Mark all operations in this transaction as rolled back
        {
            let mut logs = self.transaction_logs.lock().await;
            for log in logs.iter_mut() {
                if log.transaction_id.starts_with(&transaction.id) {
                    log.status = TransactionStatus::RolledBack;
                }
            }
        }

        // Remove from active transactions
        self.active_transactions.remove(&transaction.id);

        tracing::warn!("Rolled back transaction: {}", transaction.id);
        Ok(())
    }

    pub async fn flush(&self) -> Result<()> {
        self.search_engine.flush().await?;
        self.storage.flush().await?;
        Ok(())
    }

    async fn create_checkpoint(&self) -> Result<()> {
        let now = chrono::Utc::now();
        
        // Flush all pending operations
        self.flush().await?;

        // Update checkpoint timestamp
        {
            let mut last_checkpoint = self.stats.last_checkpoint.write();
            *last_checkpoint = Some(now);
        }

        tracing::info!("Created checkpoint at {}", now);
        Ok(())
    }

    pub fn get_stats(&self) -> IndexingStats {
        IndexingStats {
            total_operations: AtomicU64::new(self.stats.total_operations.load(Ordering::Relaxed)),
            successful_operations: AtomicU64::new(self.stats.successful_operations.load(Ordering::Relaxed)),
            failed_operations: AtomicU64::new(self.stats.failed_operations.load(Ordering::Relaxed)),
            documents_indexed: AtomicU64::new(self.stats.documents_indexed.load(Ordering::Relaxed)),
            documents_deleted: AtomicU64::new(self.stats.documents_deleted.load(Ordering::Relaxed)),
            bytes_processed: AtomicU64::new(self.stats.bytes_processed.load(Ordering::Relaxed)),
            last_checkpoint: RwLock::new(*self.stats.last_checkpoint.read()),
        }
    }
}
