use super::{
    Document<PERSON>hunker, ChunkedDocument, EnhancedTextChunk, ChunkMetadata, ContentType,
    DocumentChunkingMetadata, ChunkBoundaries, estimate_token_count, count_words,
};
use crate::config::ChunkingConfig;
use crate::error::Result;
use crate::extraction::{ChunkType, ChunkPosition};
use async_trait::async_trait;
use std::collections::HashMap;
use std::path::Path;

pub struct CodeAwareChunker {
    language_parsers: HashMap<String, Box<dyn LanguageParser>>,
}

impl CodeAwareChunker {
    pub fn new() -> Self {
        let mut language_parsers: HashMap<String, Box<dyn LanguageParser>> = HashMap::new();
        
        language_parsers.insert("rust".to_string(), Box::new(RustParser::new()));
        language_parsers.insert("python".to_string(), Box::new(PythonParser::new()));
        language_parsers.insert("javascript".to_string(), Box::new(JavaScriptParser::new()));
        language_parsers.insert("typescript".to_string(), Box::new(TypeScriptParser::new()));
        language_parsers.insert("json".to_string(), Box::new(JsonParser::new()));
        language_parsers.insert("yaml".to_string(), Box::new(YamlParser::new()));
        language_parsers.insert("markdown".to_string(), Box::new(MarkdownParser::new()));
        
        Self { language_parsers }
    }

    fn detect_language(&self, content: &str, file_path: &Path) -> String {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            match extension.to_lowercase().as_str() {
                "rs" => "rust".to_string(),
                "py" => "python".to_string(),
                "js" => "javascript".to_string(),
                "ts" => "typescript".to_string(),
                "json" => "json".to_string(),
                "yaml" | "yml" => "yaml".to_string(),
                "md" => "markdown".to_string(),
                _ => "unknown".to_string(),
            }
        } else {
            self.detect_language_from_content(content)
        }
    }

    fn detect_language_from_content(&self, content: &str) -> String {
        let lines: Vec<&str> = content.lines().take(20).collect();
        
        for line in &lines {
            let trimmed = line.trim();
            if trimmed.starts_with("fn ") || trimmed.contains("impl ") || trimmed.contains("struct ") {
                return "rust".to_string();
            }
            if trimmed.starts_with("def ") || trimmed.starts_with("class ") || trimmed.starts_with("import ") {
                return "python".to_string();
            }
            if trimmed.starts_with("function ") || trimmed.contains("const ") || trimmed.contains("let ") {
                return "javascript".to_string();
            }
        }
        
        "unknown".to_string()
    }

    fn chunk_by_language(
        &self,
        content: &str,
        language: &str,
        config: &ChunkingConfig,
        file_path: &Path,
    ) -> Result<Vec<EnhancedTextChunk>> {
        if let Some(parser) = self.language_parsers.get(language) {
            parser.parse_and_chunk(content, config, file_path)
        } else {
            self.fallback_chunk(content, config, file_path)
        }
    }

    fn fallback_chunk(
        &self,
        content: &str,
        config: &ChunkingConfig,
        file_path: &Path,
    ) -> Result<Vec<EnhancedTextChunk>> {
        let mut chunks = Vec::new();
        let lines: Vec<&str> = content.lines().collect();
        let mut current_chunk = String::new();
        let mut chunk_start_line = 0;
        let mut offset = 0;

        for (line_num, line) in lines.iter().enumerate() {
            if current_chunk.len() + line.len() > config.max_tokens * 4 && !current_chunk.is_empty() {
                if let Some(chunk) = self.create_chunk(
                    &current_chunk,
                    chunk_start_line,
                    line_num - 1,
                    offset,
                    file_path,
                    chunks.len(),
                ) {
                    chunks.push(chunk);
                }
                current_chunk.clear();
                chunk_start_line = line_num;
            }

            if !current_chunk.is_empty() {
                current_chunk.push('\n');
            }
            current_chunk.push_str(line);
            offset += line.len() + 1;
        }

        if !current_chunk.trim().is_empty() {
            if let Some(chunk) = self.create_chunk(
                &current_chunk,
                chunk_start_line,
                lines.len() - 1,
                offset,
                file_path,
                chunks.len(),
            ) {
                chunks.push(chunk);
            }
        }

        let total_chunks = chunks.len();
        for (i, chunk) in chunks.iter_mut().enumerate() {
            chunk.metadata.chunk_index = i;
            chunk.metadata.total_chunks = total_chunks;
        }

        Ok(chunks)
    }

    fn create_chunk(
        &self,
        content: &str,
        start_line: usize,
        end_line: usize,
        offset: usize,
        file_path: &Path,
        chunk_index: usize,
    ) -> Option<EnhancedTextChunk> {
        if content.trim().is_empty() {
            return None;
        }

        let token_count = estimate_token_count(content);
        let mut properties = HashMap::new();
        properties.insert("start_line".to_string(), start_line.to_string());
        properties.insert("end_line".to_string(), end_line.to_string());

        let chunk_metadata = ChunkMetadata {
            source_file: file_path.to_string_lossy().to_string(),
            chunk_index,
            total_chunks: 0,
            token_count,
            char_count: content.chars().count(),
            word_count: count_words(content),
            language: Some("unknown".to_string()),
            content_type: ContentType::Code("unknown".to_string()),
            semantic_score: None,
            overlap_start: None,
            overlap_end: None,
            context_before: None,
            context_after: None,
            properties,
        };

        let boundaries = ChunkBoundaries {
            starts_at_sentence: false,
            ends_at_sentence: false,
            starts_at_paragraph: false,
            ends_at_paragraph: false,
            starts_at_code_block: true,
            ends_at_code_block: true,
            semantic_boundary_score: None,
        };

        Some(EnhancedTextChunk {
            content: content.to_string(),
            chunk_type: ChunkType::Code("unknown".to_string()),
            position: ChunkPosition {
                page: None,
                line: Some(start_line),
                column: Some(0),
                offset: Some(offset),
            },
            metadata: chunk_metadata,
            boundaries,
        })
    }
}

#[async_trait]
impl DocumentChunker for CodeAwareChunker {
    async fn chunk_document(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
    ) -> Result<ChunkedDocument> {
        let start_time = std::time::Instant::now();
        
        let language = self.detect_language(content, file_path);
        let chunks = self.chunk_by_language(content, &language, config, file_path)?;
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        let total_tokens: usize = chunks.iter().map(|c| c.metadata.token_count).sum();
        let total_chars: usize = chunks.iter().map(|c| c.metadata.char_count).sum();
        let avg_chunk_size = if chunks.is_empty() { 0.0 } else { total_tokens as f32 / chunks.len() as f32 };
        
        let variance = if chunks.len() > 1 {
            let mean = avg_chunk_size;
            let sum_sq_diff: f32 = chunks.iter()
                .map(|c| (c.metadata.token_count as f32 - mean).powi(2))
                .sum();
            (sum_sq_diff / chunks.len() as f32).sqrt()
        } else {
            0.0
        };

        let metadata = DocumentChunkingMetadata {
            total_chunks: chunks.len(),
            total_tokens,
            total_chars,
            chunking_strategy: "code_aware".to_string(),
            processing_time_ms: processing_time,
            overlap_ratio: 0.0,
            average_chunk_size: avg_chunk_size,
            size_variance: variance,
            boundary_preservation_score: 0.95,
        };

        Ok(ChunkedDocument { chunks, metadata })
    }

    fn supports_content_type(&self, content_type: &ContentType) -> bool {
        matches!(content_type, ContentType::Code(_) | ContentType::Structured(_))
    }
    
    fn get_chunker_name(&self) -> &'static str {
        "code_aware"
    }
    
    fn estimate_chunk_count(&self, content: &str, config: &ChunkingConfig) -> usize {
        let total_tokens = estimate_token_count(content);
        (total_tokens / config.max_tokens).max(1)
    }
    
    async fn chunk_streaming<F>(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
        chunk_callback: F,
    ) -> Result<DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync,
    {
        let chunked_doc = self.chunk_document(content, file_path, config).await?;
        
        for chunk in chunked_doc.chunks {
            if !chunk_callback(chunk) {
                break;
            }
        }
        
        Ok(chunked_doc.metadata)
    }
}

trait LanguageParser: Send + Sync {
    fn parse_and_chunk(
        &self,
        content: &str,
        config: &ChunkingConfig,
        file_path: &Path,
    ) -> Result<Vec<EnhancedTextChunk>>;
}

struct RustParser;
impl RustParser {
    fn new() -> Self { Self }
}

impl LanguageParser for RustParser {
    fn parse_and_chunk(
        &self,
        content: &str,
        config: &ChunkingConfig,
        file_path: &Path,
    ) -> Result<Vec<EnhancedTextChunk>> {
        let mut chunks = Vec::new();
        let lines: Vec<&str> = content.lines().collect();
        let mut current_function = String::new();
        let mut in_function = false;
        let mut brace_count = 0;
        let mut function_start_line = 0;
        let mut offset = 0;

        for (line_num, line) in lines.iter().enumerate() {
            let trimmed = line.trim();
            
            if trimmed.starts_with("fn ") || trimmed.starts_with("pub fn ") {
                if in_function && !current_function.trim().is_empty() {
                    if let Some(chunk) = self.create_rust_chunk(
                        &current_function,
                        function_start_line,
                        line_num - 1,
                        file_path,
                        chunks.len(),
                    ) {
                        chunks.push(chunk);
                    }
                }
                current_function.clear();
                function_start_line = line_num;
                in_function = true;
                brace_count = 0;
            }

            if in_function {
                current_function.push_str(line);
                current_function.push('\n');
                
                brace_count += line.matches('{').count() as i32;
                brace_count -= line.matches('}').count() as i32;
                
                if brace_count == 0 && trimmed.ends_with('}') {
                    if let Some(chunk) = self.create_rust_chunk(
                        &current_function,
                        function_start_line,
                        line_num,
                        file_path,
                        chunks.len(),
                    ) {
                        chunks.push(chunk);
                    }
                    current_function.clear();
                    in_function = false;
                }
            }
            
            offset += line.len() + 1;
        }

        if in_function && !current_function.trim().is_empty() {
            if let Some(chunk) = self.create_rust_chunk(
                &current_function,
                function_start_line,
                lines.len() - 1,
                file_path,
                chunks.len(),
            ) {
                chunks.push(chunk);
            }
        }

        let total_chunks = chunks.len();
        for (i, chunk) in chunks.iter_mut().enumerate() {
            chunk.metadata.chunk_index = i;
            chunk.metadata.total_chunks = total_chunks;
        }

        Ok(chunks)
    }
}

impl RustParser {
    fn create_rust_chunk(
        &self,
        content: &str,
        start_line: usize,
        end_line: usize,
        file_path: &Path,
        chunk_index: usize,
    ) -> Option<EnhancedTextChunk> {
        if content.trim().is_empty() {
            return None;
        }

        let token_count = estimate_token_count(content);
        let mut properties = HashMap::new();
        properties.insert("language".to_string(), "rust".to_string());
        properties.insert("start_line".to_string(), start_line.to_string());
        properties.insert("end_line".to_string(), end_line.to_string());

        let chunk_metadata = ChunkMetadata {
            source_file: file_path.to_string_lossy().to_string(),
            chunk_index,
            total_chunks: 0,
            token_count,
            char_count: content.chars().count(),
            word_count: count_words(content),
            language: Some("rust".to_string()),
            content_type: ContentType::Code("rust".to_string()),
            semantic_score: None,
            overlap_start: None,
            overlap_end: None,
            context_before: None,
            context_after: None,
            properties,
        };

        let boundaries = ChunkBoundaries {
            starts_at_sentence: false,
            ends_at_sentence: false,
            starts_at_paragraph: false,
            ends_at_paragraph: false,
            starts_at_code_block: true,
            ends_at_code_block: true,
            semantic_boundary_score: None,
        };

        Some(EnhancedTextChunk {
            content: content.to_string(),
            chunk_type: ChunkType::Code("rust".to_string()),
            position: ChunkPosition {
                page: None,
                line: Some(start_line),
                column: Some(0),
                offset: Some(start_line * 50),
            },
            metadata: chunk_metadata,
            boundaries,
        })
    }
}

struct PythonParser;
impl PythonParser { fn new() -> Self { Self } }
impl LanguageParser for PythonParser {
    fn parse_and_chunk(&self, content: &str, _config: &ChunkingConfig, _file_path: &Path) -> Result<Vec<EnhancedTextChunk>> {
        Ok(Vec::new())
    }
}

struct JavaScriptParser;
impl JavaScriptParser { fn new() -> Self { Self } }
impl LanguageParser for JavaScriptParser {
    fn parse_and_chunk(&self, content: &str, _config: &ChunkingConfig, _file_path: &Path) -> Result<Vec<EnhancedTextChunk>> {
        Ok(Vec::new())
    }
}

struct TypeScriptParser;
impl TypeScriptParser { fn new() -> Self { Self } }
impl LanguageParser for TypeScriptParser {
    fn parse_and_chunk(&self, content: &str, _config: &ChunkingConfig, _file_path: &Path) -> Result<Vec<EnhancedTextChunk>> {
        Ok(Vec::new())
    }
}

struct JsonParser;
impl JsonParser { fn new() -> Self { Self } }
impl LanguageParser for JsonParser {
    fn parse_and_chunk(&self, content: &str, _config: &ChunkingConfig, _file_path: &Path) -> Result<Vec<EnhancedTextChunk>> {
        Ok(Vec::new())
    }
}

struct YamlParser;
impl YamlParser { fn new() -> Self { Self } }
impl LanguageParser for YamlParser {
    fn parse_and_chunk(&self, content: &str, _config: &ChunkingConfig, _file_path: &Path) -> Result<Vec<EnhancedTextChunk>> {
        Ok(Vec::new())
    }
}

struct MarkdownParser;
impl MarkdownParser { fn new() -> Self { Self } }
impl LanguageParser for MarkdownParser {
    fn parse_and_chunk(&self, content: &str, _config: &ChunkingConfig, _file_path: &Path) -> Result<Vec<EnhancedTextChunk>> {
        Ok(Vec::new())
    }
}
