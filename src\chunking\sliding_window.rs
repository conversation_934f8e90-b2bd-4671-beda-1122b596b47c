use super::{
    <PERSON>ument<PERSON><PERSON><PERSON>, ChunkedDocument, EnhancedTextChunk, ChunkMetadata, ContentType,
    DocumentChunkingMetadata, ChunkBoundaries, estimate_token_count, count_words,
};
use crate::config::ChunkingConfig;
use crate::error::Result;
use crate::extraction::{ChunkType, ChunkPosition};
use async_trait::async_trait;
use std::collections::HashMap;
use std::path::Path;

pub struct SlidingWindowChunker {
    boundary_detector: BoundaryDetector,
}

impl SlidingWindowChunker {
    pub fn new() -> Self {
        Self {
            boundary_detector: BoundaryDetector::new(),
        }
    }

    fn chunk_with_sliding_window(
        &self,
        content: &str,
        config: &ChunkingConfig,
        file_path: &Path,
    ) -> Result<Vec<EnhancedTextChunk>> {
        let words: Vec<&str> = content.split_whitespace().collect();
        let mut chunks = Vec::new();
        
        if words.is_empty() {
            return Ok(chunks);
        }

        let chunk_size_words = config.max_tokens;
        let overlap_words = config.overlap_tokens;
        let step_size = chunk_size_words.saturating_sub(overlap_words);

        let mut start = 0;
        let mut chunk_index = 0;

        while start < words.len() {
            let end = (start + chunk_size_words).min(words.len());
            let chunk_words = &words[start..end];
            
            if chunk_words.is_empty() {
                break;
            }

            let content_text = chunk_words.join(" ");
            
            if content_text.trim().is_empty() {
                start += step_size;
                continue;
            }

            let token_count = estimate_token_count(&content_text);
            
            if token_count < config.min_chunk_size {
                start += step_size;
                continue;
            }

            let boundaries = if config.preserve_sentence_boundaries {
                self.boundary_detector.adjust_for_sentence_boundaries(&content_text, start, end, &words)
            } else {
                ChunkBoundaries {
                    starts_at_sentence: false,
                    ends_at_sentence: false,
                    starts_at_paragraph: false,
                    ends_at_paragraph: false,
                    starts_at_code_block: false,
                    ends_at_code_block: false,
                    semantic_boundary_score: None,
                }
            };

            let overlap_start = if start > 0 { Some(overlap_words) } else { None };
            let overlap_end = if end < words.len() { Some(overlap_words) } else { None };

            let mut properties = HashMap::new();
            properties.insert("window_start".to_string(), start.to_string());
            properties.insert("window_end".to_string(), end.to_string());
            properties.insert("word_count".to_string(), chunk_words.len().to_string());
            
            if let Some(overlap) = overlap_start {
                properties.insert("overlap_start".to_string(), overlap.to_string());
            }
            if let Some(overlap) = overlap_end {
                properties.insert("overlap_end".to_string(), overlap.to_string());
            }

            let chunk_metadata = ChunkMetadata {
                source_file: file_path.to_string_lossy().to_string(),
                chunk_index,
                total_chunks: 0,
                token_count,
                char_count: content_text.chars().count(),
                word_count: count_words(&content_text),
                language: None,
                content_type: ContentType::Mixed,
                semantic_score: None,
                overlap_start,
                overlap_end,
                context_before: if start > 0 {
                    Some(words[start.saturating_sub(5)..start].join(" "))
                } else {
                    None
                },
                context_after: if end < words.len() {
                    Some(words[end..end.saturating_add(5).min(words.len())].join(" "))
                } else {
                    None
                },
                properties,
            };

            let chunk_type = self.detect_chunk_type(&content_text);

            chunks.push(EnhancedTextChunk {
                content: content_text,
                chunk_type,
                position: ChunkPosition {
                    page: None,
                    line: None,
                    column: None,
                    offset: Some(start),
                },
                metadata: chunk_metadata,
                boundaries,
            });

            chunk_index += 1;

            if end >= words.len() {
                break;
            }

            start += step_size;
        }

        let total_chunks = chunks.len();
        for (i, chunk) in chunks.iter_mut().enumerate() {
            chunk.metadata.chunk_index = i;
            chunk.metadata.total_chunks = total_chunks;
        }

        Ok(chunks)
    }

    fn detect_chunk_type(&self, content: &str) -> ChunkType {
        let trimmed = content.trim();
        
        if trimmed.starts_with("fn ") || 
           trimmed.starts_with("def ") || 
           trimmed.starts_with("function ") ||
           trimmed.contains("class ") ||
           trimmed.contains("struct ") {
            ChunkType::Code("unknown".to_string())
        } else if trimmed.starts_with("#") && trimmed.lines().count() == 1 {
            ChunkType::Heading(1)
        } else if trimmed.starts_with("- ") || trimmed.starts_with("* ") || trimmed.starts_with("1. ") {
            ChunkType::List
        } else if trimmed.starts_with("> ") {
            ChunkType::Quote
        } else {
            ChunkType::Paragraph
        }
    }

    fn calculate_overlap_ratio(&self, chunks: &[EnhancedTextChunk]) -> f32 {
        if chunks.len() < 2 {
            return 0.0;
        }

        let mut total_overlap = 0;
        let mut total_content = 0;

        for chunk in chunks {
            total_content += chunk.metadata.char_count;
            if let Some(overlap_start) = chunk.metadata.overlap_start {
                total_overlap += overlap_start * 4;
            }
            if let Some(overlap_end) = chunk.metadata.overlap_end {
                total_overlap += overlap_end * 4;
            }
        }

        if total_content > 0 {
            total_overlap as f32 / total_content as f32
        } else {
            0.0
        }
    }
}

#[async_trait]
impl DocumentChunker for SlidingWindowChunker {
    async fn chunk_document(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
    ) -> Result<ChunkedDocument> {
        let start_time = std::time::Instant::now();
        
        let chunks = self.chunk_with_sliding_window(content, config, file_path)?;
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        let total_tokens: usize = chunks.iter().map(|c| c.metadata.token_count).sum();
        let total_chars: usize = chunks.iter().map(|c| c.metadata.char_count).sum();
        let avg_chunk_size = if chunks.is_empty() { 0.0 } else { total_tokens as f32 / chunks.len() as f32 };
        
        let variance = if chunks.len() > 1 {
            let mean = avg_chunk_size;
            let sum_sq_diff: f32 = chunks.iter()
                .map(|c| (c.metadata.token_count as f32 - mean).powi(2))
                .sum();
            (sum_sq_diff / chunks.len() as f32).sqrt()
        } else {
            0.0
        };

        let overlap_ratio = self.calculate_overlap_ratio(&chunks);

        let boundary_score = chunks.iter()
            .map(|c| {
                let mut score = 0.0;
                if c.boundaries.starts_at_sentence { score += 0.25; }
                if c.boundaries.ends_at_sentence { score += 0.25; }
                if c.boundaries.starts_at_paragraph { score += 0.25; }
                if c.boundaries.ends_at_paragraph { score += 0.25; }
                score
            })
            .sum::<f32>() / chunks.len().max(1) as f32;

        let metadata = DocumentChunkingMetadata {
            total_chunks: chunks.len(),
            total_tokens,
            total_chars,
            chunking_strategy: "sliding_window".to_string(),
            processing_time_ms: processing_time,
            overlap_ratio,
            average_chunk_size: avg_chunk_size,
            size_variance: variance,
            boundary_preservation_score: boundary_score,
        };

        Ok(ChunkedDocument { chunks, metadata })
    }

    fn supports_content_type(&self, _content_type: &ContentType) -> bool {
        true
    }
    
    fn get_chunker_name(&self) -> &'static str {
        "sliding_window"
    }
    
    fn estimate_chunk_count(&self, content: &str, config: &ChunkingConfig) -> usize {
        let total_tokens = estimate_token_count(content);
        let chunk_size = config.max_tokens;
        let overlap = config.overlap_tokens;
        let step_size = chunk_size.saturating_sub(overlap);
        
        if step_size == 0 {
            return 1;
        }
        
        ((total_tokens + step_size - 1) / step_size).max(1)
    }
    
    async fn chunk_streaming<F>(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
        chunk_callback: F,
    ) -> Result<DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync,
    {
        let start_time = std::time::Instant::now();
        
        let words: Vec<&str> = content.split_whitespace().collect();
        let chunk_size_words = config.max_tokens;
        let overlap_words = config.overlap_tokens;
        let step_size = chunk_size_words.saturating_sub(overlap_words);

        let mut start = 0;
        let mut chunk_index = 0;
        let mut total_tokens = 0;
        let mut total_chars = 0;

        while start < words.len() {
            let end = (start + chunk_size_words).min(words.len());
            let chunk_words = &words[start..end];
            
            if chunk_words.is_empty() {
                break;
            }

            let content_text = chunk_words.join(" ");
            
            if content_text.trim().is_empty() {
                start += step_size;
                continue;
            }

            let token_count = estimate_token_count(&content_text);
            
            if token_count < config.min_chunk_size {
                start += step_size;
                continue;
            }

            let boundaries = ChunkBoundaries {
                starts_at_sentence: false,
                ends_at_sentence: false,
                starts_at_paragraph: false,
                ends_at_paragraph: false,
                starts_at_code_block: false,
                ends_at_code_block: false,
                semantic_boundary_score: None,
            };

            let mut properties = HashMap::new();
            properties.insert("window_start".to_string(), start.to_string());
            properties.insert("window_end".to_string(), end.to_string());

            let chunk_metadata = ChunkMetadata {
                source_file: file_path.to_string_lossy().to_string(),
                chunk_index,
                total_chunks: 0,
                token_count,
                char_count: content_text.chars().count(),
                word_count: count_words(&content_text),
                language: None,
                content_type: ContentType::Mixed,
                semantic_score: None,
                overlap_start: if start > 0 { Some(overlap_words) } else { None },
                overlap_end: if end < words.len() { Some(overlap_words) } else { None },
                context_before: None,
                context_after: None,
                properties,
            };

            let chunk = EnhancedTextChunk {
                content: content_text,
                chunk_type: self.detect_chunk_type(&chunk_words.join(" ")),
                position: ChunkPosition {
                    page: None,
                    line: None,
                    column: None,
                    offset: Some(start),
                },
                metadata: chunk_metadata,
                boundaries,
            };

            total_tokens += token_count;
            total_chars += chunk.metadata.char_count;

            if !chunk_callback(chunk) {
                break;
            }

            chunk_index += 1;

            if end >= words.len() {
                break;
            }

            start += step_size;
        }

        let processing_time = start_time.elapsed().as_millis() as u64;
        let avg_chunk_size = if chunk_index > 0 { total_tokens as f32 / chunk_index as f32 } else { 0.0 };

        let metadata = DocumentChunkingMetadata {
            total_chunks: chunk_index,
            total_tokens,
            total_chars,
            chunking_strategy: "sliding_window".to_string(),
            processing_time_ms: processing_time,
            overlap_ratio: if overlap_words > 0 { overlap_words as f32 / chunk_size_words as f32 } else { 0.0 },
            average_chunk_size: avg_chunk_size,
            size_variance: 0.0,
            boundary_preservation_score: 0.5,
        };

        Ok(metadata)
    }
}

struct BoundaryDetector;

impl BoundaryDetector {
    fn new() -> Self {
        Self
    }

    fn adjust_for_sentence_boundaries(
        &self,
        content: &str,
        _start: usize,
        _end: usize,
        _words: &[&str],
    ) -> ChunkBoundaries {
        let starts_at_sentence = content.trim_start().chars().next()
            .map(|c| c.is_uppercase())
            .unwrap_or(false);
        
        let ends_at_sentence = content.trim_end().ends_with(&['.', '!', '?'][..]);
        
        let starts_at_paragraph = content.starts_with('\n') || content.trim_start() == content;
        let ends_at_paragraph = content.ends_with('\n') || content.trim_end() == content;

        ChunkBoundaries {
            starts_at_sentence,
            ends_at_sentence,
            starts_at_paragraph,
            ends_at_paragraph,
            starts_at_code_block: false,
            ends_at_code_block: false,
            semantic_boundary_score: Some(0.7),
        }
    }
}
