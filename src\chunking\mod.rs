use crate::config::ChunkingConfig;
use crate::error::Result;
use crate::extraction::{TextChunk, ChunkType, ChunkPosition};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;

pub mod semantic;
pub mod code_aware;
pub mod sliding_window;
pub mod adaptive;
pub mod benchmarks;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChunkMetadata {
    pub source_file: String,
    pub chunk_index: usize,
    pub total_chunks: usize,
    pub token_count: usize,
    pub char_count: usize,
    pub word_count: usize,
    pub language: Option<String>,
    pub content_type: ContentType,
    pub semantic_score: Option<f32>,
    pub overlap_start: Option<usize>,
    pub overlap_end: Option<usize>,
    pub context_before: Option<String>,
    pub context_after: Option<String>,
    pub properties: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContentType {
    NaturalLanguage,
    Code(String),
    Structured(String),
    Mixed,
    Unknown,
}

#[derive(Debug, Clone)]
pub struct ChunkedDocument {
    pub chunks: Vec<EnhancedTextChunk>,
    pub metadata: DocumentChunkingMetadata,
}

#[derive(Debug, Clone)]
pub struct EnhancedTextChunk {
    pub content: String,
    pub chunk_type: ChunkType,
    pub position: ChunkPosition,
    pub metadata: ChunkMetadata,
    pub boundaries: ChunkBoundaries,
}

#[derive(Debug, Clone)]
pub struct ChunkBoundaries {
    pub starts_at_sentence: bool,
    pub ends_at_sentence: bool,
    pub starts_at_paragraph: bool,
    pub ends_at_paragraph: bool,
    pub starts_at_code_block: bool,
    pub ends_at_code_block: bool,
    pub semantic_boundary_score: Option<f32>,
}

#[derive(Debug, Clone)]
pub struct DocumentChunkingMetadata {
    pub total_chunks: usize,
    pub total_tokens: usize,
    pub total_chars: usize,
    pub chunking_strategy: String,
    pub processing_time_ms: u64,
    pub overlap_ratio: f32,
    pub average_chunk_size: f32,
    pub size_variance: f32,
    pub boundary_preservation_score: f32,
}

#[async_trait]
pub trait DocumentChunker: Send + Sync {
    async fn chunk_document(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
    ) -> Result<ChunkedDocument>;

    fn supports_content_type(&self, content_type: &ContentType) -> bool;
    
    fn get_chunker_name(&self) -> &'static str;
    
    fn estimate_chunk_count(&self, content: &str, config: &ChunkingConfig) -> usize;
    
    async fn chunk_streaming<F>(
        &self,
        content: &str,
        file_path: &Path,
        config: &ChunkingConfig,
        chunk_callback: F,
    ) -> Result<DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync;
}

pub enum ChunkerType {
    Semantic(semantic::SemanticChunker),
    CodeAware(code_aware::CodeAwareChunker),
    SlidingWindow(sliding_window::SlidingWindowChunker),
    Adaptive(adaptive::AdaptiveChunker),
}

impl ChunkerType {
    pub async fn chunk_document(
        &self,
        content: &str,
        file_path: &std::path::Path,
        config: &ChunkingConfig,
    ) -> Result<ChunkedDocument> {
        match self {
            ChunkerType::Semantic(c) => c.chunk_document(content, file_path, config).await,
            ChunkerType::CodeAware(c) => c.chunk_document(content, file_path, config).await,
            ChunkerType::SlidingWindow(c) => c.chunk_document(content, file_path, config).await,
            ChunkerType::Adaptive(c) => c.chunk_document(content, file_path, config).await,
        }
    }

    pub fn supports_content_type(&self, content_type: &ContentType) -> bool {
        match self {
            ChunkerType::Semantic(c) => c.supports_content_type(content_type),
            ChunkerType::CodeAware(c) => c.supports_content_type(content_type),
            ChunkerType::SlidingWindow(c) => c.supports_content_type(content_type),
            ChunkerType::Adaptive(c) => c.supports_content_type(content_type),
        }
    }

    pub fn get_chunker_name(&self) -> &'static str {
        match self {
            ChunkerType::Semantic(c) => c.get_chunker_name(),
            ChunkerType::CodeAware(c) => c.get_chunker_name(),
            ChunkerType::SlidingWindow(c) => c.get_chunker_name(),
            ChunkerType::Adaptive(c) => c.get_chunker_name(),
        }
    }
}

pub struct ChunkingEngine {
    chunkers: Vec<ChunkerType>,
    config: ChunkingConfig,
}

impl ChunkingEngine {
    pub fn new(config: ChunkingConfig) -> Self {
        let mut chunkers = Vec::new();

        chunkers.push(ChunkerType::Semantic(semantic::SemanticChunker::new()));
        chunkers.push(ChunkerType::CodeAware(code_aware::CodeAwareChunker::new()));
        chunkers.push(ChunkerType::SlidingWindow(sliding_window::SlidingWindowChunker::new()));
        chunkers.push(ChunkerType::Adaptive(adaptive::AdaptiveChunker::new()));

        Self { chunkers, config }
    }

    pub async fn chunk_document(
        &self,
        content: &str,
        file_path: &Path,
        content_type: Option<ContentType>,
    ) -> Result<ChunkedDocument> {
        let detected_content_type = content_type.unwrap_or_else(|| self.detect_content_type(content, file_path));
        
        let chunker = self.select_optimal_chunker(&detected_content_type)?;
        
        chunker.chunk_document(content, file_path, &self.config).await
    }

    pub async fn chunk_streaming<F>(
        &self,
        content: &str,
        file_path: &Path,
        content_type: Option<ContentType>,
        chunk_callback: F,
    ) -> Result<DocumentChunkingMetadata>
    where
        F: Fn(EnhancedTextChunk) -> bool + Send + Sync,
    {
        let detected_content_type = content_type.unwrap_or_else(|| self.detect_content_type(content, file_path));
        
        let chunker = self.select_optimal_chunker(&detected_content_type)?;
        
        chunker.chunk_streaming(content, file_path, &self.config, chunk_callback).await
    }

    fn detect_content_type(&self, content: &str, file_path: &Path) -> ContentType {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            match extension.to_lowercase().as_str() {
                "rs" => ContentType::Code("rust".to_string()),
                "py" => ContentType::Code("python".to_string()),
                "js" => ContentType::Code("javascript".to_string()),
                "ts" => ContentType::Code("typescript".to_string()),
                "json" => ContentType::Structured("json".to_string()),
                "yaml" | "yml" => ContentType::Structured("yaml".to_string()),
                "toml" => ContentType::Structured("toml".to_string()),
                "md" => ContentType::Structured("markdown".to_string()),
                "txt" => {
                    if self.looks_like_code(content) {
                        ContentType::Code("unknown".to_string())
                    } else {
                        ContentType::NaturalLanguage
                    }
                }
                _ => ContentType::Unknown,
            }
        } else {
            ContentType::Unknown
        }
    }

    fn looks_like_code(&self, content: &str) -> bool {
        let lines: Vec<&str> = content.lines().collect();
        if lines.is_empty() {
            return false;
        }

        let mut code_indicators = 0;
        let sample_size = lines.len().min(50);

        for line in &lines[..sample_size] {
            let trimmed = line.trim();
            if trimmed.is_empty() {
                continue;
            }

            if trimmed.starts_with("//") || 
               trimmed.starts_with("#") ||
               trimmed.starts_with("/*") ||
               trimmed.contains("function ") ||
               trimmed.contains("def ") ||
               trimmed.contains("class ") ||
               trimmed.contains("import ") ||
               trimmed.contains("from ") ||
               trimmed.contains("use ") ||
               trimmed.contains("fn ") ||
               trimmed.contains("struct ") ||
               trimmed.contains("impl ") ||
               trimmed.contains("let ") ||
               trimmed.contains("const ") ||
               trimmed.contains("var ") ||
               trimmed.ends_with(";") ||
               trimmed.ends_with("{") ||
               trimmed.ends_with("}") {
                code_indicators += 1;
            }
        }

        (code_indicators as f32 / sample_size as f32) > 0.3
    }

    fn select_optimal_chunker(&self, content_type: &ContentType) -> Result<&ChunkerType> {
        use crate::config::ChunkingStrategy;

        match self.config.strategy {
            ChunkingStrategy::Semantic => {
                self.chunkers.iter()
                    .find(|c| c.get_chunker_name() == "semantic")
                    .ok_or_else(|| anyhow::anyhow!("Semantic chunker not found"))
            }
            ChunkingStrategy::CodeAware => {
                self.chunkers.iter()
                    .find(|c| c.get_chunker_name() == "code_aware")
                    .ok_or_else(|| anyhow::anyhow!("Code-aware chunker not found"))
            }
            ChunkingStrategy::SlidingWindow => {
                self.chunkers.iter()
                    .find(|c| c.get_chunker_name() == "sliding_window")
                    .ok_or_else(|| anyhow::anyhow!("Sliding window chunker not found"))
            }
            ChunkingStrategy::Adaptive => {
                self.chunkers.iter()
                    .find(|c| c.get_chunker_name() == "adaptive")
                    .ok_or_else(|| anyhow::anyhow!("Adaptive chunker not found"))
            }
            ChunkingStrategy::FixedSize => {
                self.chunkers.iter()
                    .find(|c| c.get_chunker_name() == "sliding_window")
                    .ok_or_else(|| anyhow::anyhow!("Fixed size chunker not found"))
            }
        }
    }
}

pub fn estimate_token_count(text: &str) -> usize {
    let word_count = text.split_whitespace().count();
    ((word_count as f32) * 1.33) as usize
}

pub fn count_words(text: &str) -> usize {
    text.split_whitespace().count()
}

pub fn count_sentences(text: &str) -> usize {
    text.matches(&['.', '!', '?'][..]).count()
}
